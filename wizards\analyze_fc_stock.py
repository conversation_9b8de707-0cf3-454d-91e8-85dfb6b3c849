from odoo import _, api, models, fields

class AksAnalyzeFcStock(models.TransientModel):
    """Analyze FC Stock"""
    _name = "aks.analyze.fc.stock"
    _description = "Analyze FC Stock"

    product_id = fields.Many2one("product.product", string="Product")
    odoo_qty = fields.Float(string="Odoo Quantity", compute="_compute_odoo_qty", readonly=True)
    fc_qty = fields.Float(string="FC Quantity", readonly=True)
    diff_qty = fields.Float(string="Difference Quantity", compute="_compute_diff_qty", readonly=True)
    line_ids = fields.One2many("aks.analyze.fc.stock.line", "wizard_id", string="Stock Lines")

    def _compute_odoo_qty(self):
        for rec in self:
            rec.odoo_qty = rec.product_id.qty_available

    def _compute_diff_qty(self):
        for rec in self:
            rec.diff_qty = rec.odoo_qty - rec.fc_qty
    
    def default_get(self, fields):
        """https://www.public.ankorstore-sandbox.com/api/v1/fulfillment/lots"""
        res = super(AksAnalyzeFcStock, self).default_get(fields)
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        lots = session.iterate("fulfillment/lots")
        for lot in lots:
            # 
            

        return res

class AksAnalyzeFcStockLine(models.TransientModel):
    """Analyze FC Stock Line"""
    _name = "aks.analyze.fc.stock.line"
    _description = "Analyze FC Stock Line"

    wizard_id = fields.Many2one("aks.analyze.fc.stock", string="Wizard", readonly=True, ondelete="cascade")
    lot_fc_number = fields.Char(string="Lot FC Number")
    lot_id = fields.Many2one("stock.production.lot", string="Lot")
    lot_odoo_qty = fields.Float(string="Odoo Quantity", readonly=True)
    lot_fc_qty = fields.Float(string="FC Quantity", readonly=True)
    state = fields.Selection(
        [
            ("equal", "Equal"),
            ("more", "More"),
            ("less", "Less"),
            ("not_found", "Not Found")
        ],
        string="State",
        readonly=True,
        compute="_compute_state"
    )
    
    def _compute_state(self):
        for rec in self:
            if rec.lot_odoo_qty == rec.lot_fc_qty:
                rec.state = "equal"
            elif rec.lot_odoo_qty > rec.lot_fc_qty:
                rec.state = "more"
            elif rec.lot_odoo_qty < rec.lot_fc_qty:
                rec.state = "less"
            else:
                rec.state = "not_found"