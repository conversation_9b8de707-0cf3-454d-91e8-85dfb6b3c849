
from odoo import _, api, models, fields


class AksQuoteLookupLine(models.TransientModel):
    """Quote Selection"""

    _name = "aks.quote.lookup"
    _description = "Quote Selection"

    wizard_id = fields.Many2one("choose.delivery.carrier", string="Wizard", ondelete="cascade")
    currency = fields.Char(string="Currency")
    collection_method = fields.Char(string="Collection Method")
    quote_uuid = fields.Char(string="Quote UUID")
    carrier_code = fields.Char(string="Carrier Code")
    service_code = fields.Char(string="Service Code")
    service_commercial_name = fields.Char(string="Service Commercial Name")
    amount = fields.Float(string="Amount")

    def update_quote(self):
        self.ensure_one()
        self.wizard_id.delivery_price = self.amount
        self.wizard_id.service_commercial_name = self.service_commercial_name
        self.wizard_id.quote_uuid = self.quote_uuid
        return {
            'name': _('Add a shipping method'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'choose.delivery.carrier',
            'res_id': self.wizard_id.id,
            'target': 'new',
        }


class AksPackageLine(models.TransientModel):
    """Package Line"""    
    _name = "aks.package.line"
    _description = "Package Line"

    wizard_id = fields.Many2one("choose.delivery.carrier", string="Wizard", ondelete="cascade",readonly=True)
    package_id = fields.Many2one("stock.package.type", string="Package Type")
    parcel_number = fields.Integer(string="Parcel Number", default=1)
    parcel_weight = fields.Float(string="Weight by parcel(Kg)")