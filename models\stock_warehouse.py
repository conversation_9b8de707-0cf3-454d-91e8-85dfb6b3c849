# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import logging

from odoo import api, models, fields, _
from odoo.exceptions import ValidationError


_logger = logging.getLogger(__name__)


class StockWarehouse(models.Model):
    """
    Propagated every locations, pickings and stock moves
    created in the warehouse.
    """
    _inherit = 'stock.warehouse'

    is_aks_fc = fields.Boolean(
        string="Is AKS Fulfillment Center", default=False)

    @api.constrains('is_aks_fc')
    def _check_is_aks_fc(self):
        """
        Check if there is only one AKS Fulfillment Center per company.
        """
        for record in self:
            if record.is_aks_fc:
                if self.search_count([('is_aks_fc', '=', True), ('company_id', '=', record.company_id.id)]) > 1:
                    raise ValidationError(_("Only one AKS Fulfillment Center is allowed per company."))

    def _archive_operation_type(self):
        """
        Archives the internal operation types of the warehouse.
        This method searches for all internal operation types
        ('code' = 'internal') associated with the current warehouse
        and sets them to inactive. Additionally, it marks these operation
        types as 'is_archive_by_default', indicating they were archived by
        this automated process.
        Intended to be called when certain conditions are met that require the
        deactivation of internal operations, such as converting a warehouse
        into an AKS Fulfillment Center.
        """
        operation_type_ids = self.env['stock.picking.type'].search([
                ('warehouse_id', '=', self.id),
                ('code', '=', 'internal'),
            ])
        operation_type_ids.write({'active': False})
        operation_type_ids.write({'is_archive_by_default': True})
