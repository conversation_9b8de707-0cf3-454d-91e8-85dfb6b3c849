# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
from odoo import api, models, fields, _
from odoo.exceptions import UserError
from odoo.tools import float_repr
from odoo.tools.safe_eval import const_eval
from markupsafe import Markup
from odoo.tools import pdf

import logging

_logger = logging.getLogger(__name__)


class ProviderAks(models.Model):
    _inherit = 'delivery.carrier'
    """_summary_ = 'ANKORSTORE delivery carrier'

    """
    delivery_type = fields.Selection(selection_add=[
        ('aks', "Ankorstore"),
    ], ondelete={'aks': lambda recs: recs.write({'delivery_type': 'fixed', 'fixed_price': 0})})
    aks_label_stock_type = fields.Selection([('A4', 'A4')], default='A4')
    aks_label_format = fields.Selection([('PDF', 'PDF')], default='PDF')
    aks_default_package_type_id = fields.Many2one('stock.package.type',
                                                  string='Default Package Type')

    def aks_rate_shipment(self, order):
        """ Get shipping cost from Ankorstore API by quotes
            Implemented to avoid standard calls
            not used in the module (quotes)
        """
        return {'success': True,
                'price': 0,
                'error_message': False,
                'warning_message': False}

    def aks_send_shipping(self, pickings):
        """ Send shipping request to Ankorstore API
        """
        res = []
        try:
            res = [{'exact_price': 0,
                    'tracking_number': False}]
            for picking in pickings:
                if picking.is_aks_fc:
                    return res
                # confirm latest quotes ? or reload cost quotes from new shipping
                latest_quote = picking.aks_latest_quote or picking.sale_id.aks_latest_quote
                if not latest_quote:
                    raise UserError(_("No latest quote found for the delivery order"))
                backend = self.env["backend.aks"].get_instance()
                session = backend.get_session()
                url = self.env["backend.aks"].get_base_url()
                path = url + f"/api/v1/shipping-quotes/{latest_quote}/confirm"
                ret = session.http_request('POST', path, None)
                
            return res
        except Exception as e:
            _logger.error("Error sending shipping request to Ankorstore API: %s", str(e))
            self.env['aks.sync.error']._logme(model='delivery.carrier',
                                              operation='aks_send_shipping',
                                              name=self.name,
                                              error_message=str(e))

    def aks_get_tracking_link(self, picking):
        return picking.aks_tracking_link

    def aks_get_shipping_overview(self, picking):
        """ Get shipping overview from Ankorstore API
        retrieve labels and tracking number
        """
        res = []
        try:
            if picking.aks_id:
                # V15 : not exist
                #  packages = self._get_packages_from_picking(picking, self.aks_default_package_type_id)
                packages = picking.package_ids
                tracking_link = False
                tracking_number = False
                logmessage = False
                backend = self.env["backend.aks"].get_instance()
                session = backend.get_session()
                path = f"orders/{picking.aks_id}"
                order = session.get(path)
                shipping_overview = order.resource.shippingOverview
                if shipping_overview.transaction:
                    if shipping_overview.transaction.tracking:
                        tracking_link = shipping_overview.transaction.tracking.trackingLink
                        tracking_number = shipping_overview.transaction.tracking.trackingNumber

                pdf_files = []
                attachments = []
                if not picking.is_aks_fc and shipping_overview.parcels:
                    for parcel in shipping_overview.parcels:
                        url = parcel.get('trackedPackage').get('labelUrl')
                        pdf_file = backend.make_api_request('GET', '', data=None, params=None, headers=None,  force_url=url)
                        pdf_files = pdf_files + [bytes(pdf_file.content)]

                    logmessage = _("Return label generated") + Markup("<br/><b>") + \
                                _("Tracking Numbers:") + Markup("</b> ") + tracking_number + \
                                Markup("<br/><b>") + _("Packages:") + Markup("</b> ") + \
                                    ','.join([p.name for p in packages if p.name])

                    attachments = [('%s.pdf' % (self._get_delivery_label_prefix()), pdf.merge_pdf([x for x in pdf_files]))]
                if not logmessage:
                    logmessage = _("Update Tracking number %s") % (tracking_number)
                picking.message_post(body=logmessage, attachments=attachments)
                picking.aks_tracking_link = tracking_link
                picking.carrier_tracking_ref = tracking_number

            return res
        except Exception as e:
            _logger.error("Error getting shipping overview from Ankorstore API: %s", str(e))            
            self.env['aks.sync.error']._logme(model='delivery.carrier', operation='aks_get_shipping_overview', name=picking.name, error_message=str(e))

    def aks_get_return_label(self, picking, tracking_number=None, origin_date=None):
        #  TODO : implement return label
        return True
