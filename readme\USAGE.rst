# From an AKS internal order:
    1) create a sale order in Odoo 
    2) action_confirm --> standard Odoo routes for sale order, AKS delivey provider can be choose on the sale order and pickings
    3) Compute the state of pickings and update the AKS internal order, get tracking/label from AKS and update the last picking

    4) Main workflows :

    Wehhook : order.brand_created - A new order is created for the brand : create a sale order in Odoo, (quote send), cron = check if threre's order.brand_created without ask_id
    
    User :
    --> action_confirm - Odoo Order is confirmed : create pickings(propagate order aks infos) and PO (AKS fees).
        -> order.brand_accepted - Brand accepts the order
    --> action_reject_order(reason) - <PERSON> rejects the order
        -> order.brand_rejected - Brand rejects the order
   
    AKS :
    order.billing_address_updated - Billing address is updated

    order.shipping_address_updated - Shipping address is updated
    order.shipping_labels_generated - When shipping with Ankorstore
    
    order.shipped - Fired by either shipping with custom or with Ankorstore
    order.shipment_received - Retailer confirms reception of the order
    order.shipment_refused - <PERSON>tailer refuses the order
    
    order.brand_paid - Ankorstore pays the brand for the order
    
    order.cancelled - Order is cancelled
    
    order.brand_accepted_reverted - Reverted Brand accepting the order
    order.shipping_labels_generated_reverted - Reverted shipment labels generation, when shipping with Ankorstore

    4) Create the invoices (SO/ PO) and reconcile them with one payments =  out(sale)- in(AKS fees)
    Risk : AKS invoice the retailer ( fiscal position Retailer) and from odoo, the brand invoice Ankorstore ( fiscal position ANKORSTORE) ?
    Custom taxes for AKS fees ?
    Aks : Account expert
    Todo : check after invoice creation in odoo if AKS amounts (VAT, TTC) are correct versus Odoo calculation


# Form Odoo to AKS, choose AKS : create an external order in AKS
