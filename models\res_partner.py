# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.


from odoo import api, fields, models, exceptions, _
from odoo.exceptions import ValidationError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    aks_retailer_id = fields.Char(
        string='AKS Retailer ID',
    )
    aks_store_name = fields.Char(string='AKS Store Name')

    def check_aks_partner(self):
        """
        Check if the partner is valid for the external order
        """
        required_fields = ['country_id', 'zip', 'city', 'street', 'name', 'email']
        for partner in self:
            for field in required_fields:
                if not partner[field]:
                    raise ValidationError(_("The field '%s' is required for the partner %s") % (field, partner.name))
        for partner in self:
            if not partner['phone'] and not partner['mobile']:
                raise ValidationError(_("You must provide at least one of the following fields: Phone, Mobile"))
        return True
