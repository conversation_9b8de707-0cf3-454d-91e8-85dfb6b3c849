# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import logging
import time
import uuid
from datetime import datetime

from odoo import _, api, fields, models

# from .jsonapi_client import Session, Filter, Inclusion, Modifier
from odoo.addons.connector_ankorstore.aks.jsonapi_client import Filter
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    """
    Internal order = sale order, standard odoo routes (or could be aks FC), AKS DELIVERY PROVIDER is available
    External order = AKS picking on an existing odoo order (MUST BE before confimation map master order on sale order : aks_external_master_order),
    TODO:
        lines routes (ex: as partial dropship, or partial MTO or customs routes) are not allowed (map all sale order lines one to one AKS picking)
        delivery provider need to be AKS in readonly mode (get tracking only) and fees
    """

    _inherit = "sale.order"

    aks_id = fields.Char(string="AKS Order UUID", copy=False, readonly=True)
    aks_internal_master_order = fields.Char(string="Ankorstore Internal Master order UUID", readonly=True, copy=False)
    aks_last_synchronization = fields.Datetime("Last synchronization date", readonly=True, copy=False)
    last_aks_status = fields.Selection(
        [
            ("ankor_confirmed", "New Ankorstore order"),
            ("awaiting_fulfillment", "Order is waiting for fulfillment"),
            ("cancelled", "Order is cancelled"),
            ("shipped", "Order is shipped from the warehouse"),
            ("received", "Order is arrived and completed"),
            ("rejected", "Order is rejected by the brand"),
            ("fulfillment_requested", "Fulfillment requested"),
            ("shipping_labels_generated", "Shipping labels generated"),
            ("reception_refused", "Reception refused by the retailer"),
            ("invoiced", "Order is invoiced"),
            ("brand_paid", "Brand paid"),
            ("brand_confirmed", "Brand confirmed the order"),
        ],
        string="Last AKS status",
        readonly=True,
        copy=False,
    )
    aks_fees = fields.One2many("aks.fees.line", "order_id", string="AKS Fees", readonly=True, copy=False)
    aks_reject_reason = fields.Selection(
        [
            ("BRAND_ALREADY_HAS_CUSTOMER_IN_THE_AREA", "Brand already has customer in the area"),
            ("BRAND_CANNOT_DELIVER_TO_THE_AREA", "Brand cannot deliver to the area"),
            ("BRAND_HAS_EXCLUSIVE_DISTRIBUTOR_IN_THE_REGION", "Brand has exclusive distributor in the region"),
            ("BUYER_NOT_A_RETAILER", "Buyer not a retailer"),
            ("ORDER_ITEMS_PRICES_INCORRECT", "Order items prices incorrect"),
            ("PAYMENT_ISSUES_WITH_RETAILER", "Payment issues with retailer"),
            ("PREPARATION_TIME_TOO_HIGH", "Preparation time too high"),
            ("PRODUCT_OUT_OF_STOCK", "Product out of stock"),
            ("PURCHASE_NOT_FOR_RESALE", "Purchase not for resale"),
            ("RETAILER_AGREED_TO_DO_CHANGES_TO_ORDER", "Retailer agreed to do changes to order"),
            ("RETAILER_NOT_GOOD_FIT_FOR_BRAND", "Retailer not good fit for brand"),
            ("RETAILER_VAT_NUMBER_MISSING", "Retailer VAT number missing"),
            ("OTHER", "Other"),
        ],
        string="AKS Reject Reason",
        copy=False,
    )

    aks_reject_details = fields.Char(string="Reject Details", size=1000, copy=False)

    aks_mode = fields.Selection([("FFB", "Internal Fulfillment"), ("FFA", "Fulfillment by Ankorstore")], string="Fulfillment Mode", default="FFB")

    aks_external_master_order = fields.Char(string="AKS UUID External Order", readonly=True, copy=False)

    aks_latest_quote = fields.Char(string="AKS Delivery Latest Quote", copy=False)

    _sql_constraints = [
        ("aks_id_uniq", "unique(aks_id)", "Ankorstore Order UUID must be unique"),
        ("aks_external_master_order_uniq", "unique(aks_external_master_order)", "Ankorstore External order UUID must be unique"),
        ("aks_internal_master_order_uniq", "unique(aks_internal_master_order)", "Ankorstore Internal Master order UUID must be unique"),
    ]

    @api.depends("aks_mode")
    def _compute_wh(self):
        """
        Compute warehouse based on AKS mode.
        """
        params = self.env["backend.aks"].get_params()
        if self.aks_mode == "FFA":
            # If AKS mode is 'FFA', set warehouse to first AKS fulfillment center
            self.warehouse_id = self.env["stock.warehouse"].search([("is_aks_fc", "=", True), ("company_id", "=", params.company_id.id)], limit=1)
        else:
            #  _get_model_defaults doesnt exist in V15, replaced with get_model_defaults
            self.warehouse_id = self.env["ir.default"].with_company(self.company_id.id)._get_model_defaults("sale.order").get("warehouse_id")

    @api.model
    def default_get(self, fields):
        res = super().default_get(fields)
        params = self.env["backend.aks"].get_params()
        if "aks_mode" in fields:
            if res.get("aks_mode") == "FFA":
                res["warehouse_id"] = self.env["stock.warehouse"].search([("is_aks_fc", "=", True), ("company_id", "=", params.company_id.id)], limit=1).id

        return res

    @api.onchange("aks_mode")
    def _onchange_aks_mode(self):
        """
        Update warehouse when AKS mode changes.
        """
        # If AKS mode is 'FFA', set warehouse to first AKS fulfillment center
        params = self.env["backend.aks"].get_params()
        if self.aks_mode == "FFA":
            if not params.aks_active_fc:
                raise ValidationError(_("No fulfillment center is active"))
            self.warehouse_id = self.env["stock.warehouse"].search([("is_aks_fc", "=", True), ("company_id", "=", params.company_id.id)], limit=1).id
        else:
            if self.aks_id and params.aks_active_fc and params.aks_auto_confirm_orders:
                raise ValidationError(_("You cannot change the mode to FFB if the auto confirm orders is activated"))
            else:
                self.warehouse_id = self._get_warehouse_available()

    def action_reject(self):
        """Reject the order in the FC"""
        if self.aks_id and self.aks_reject_reason:
            backend = self.env["backend.aks"].get_instance()
            session = backend.get_session()
            if self.aks_reject_reason == "OTHER" and self.aks_reject_details:
                dict_to_send = {"data": {"type": "brand-rejects", "attributes": {"rejectType": self.aks_reject_reason, "rejectReason": self.aks_reject_details}}}
            else:
                dict_to_send = {
                    "data": {
                        "type": "brand-rejects",
                        "attributes": {
                            "rejectType": self.aks_reject_reason,
                        },
                    }
                }
            url = self.env["backend.aks"].get_base_url()
            path = url + f"/api/v1/orders/{self.aks_id}/-actions/transition"
            try:
                session.http_request("POST", path, dict_to_send)
                body = _("The order has been rejected in the FC with the reason %s", self.aks_reject_reason)
                self.message_post(body=body)
                self.state = "sent"
                # waiting webhook aks cancel
            except Exception as e:
                self.env["aks.sync.error"]._logme(
                    model="sale.order", operation="action_reject", name=self.name, error_message=_("Error while rejecting the order in the FC: %s" % e)
                )
                raise ValidationError(_("Error while rejecting the order in the FC: %s" % e))

    def action_reject_aks_orders(self):
        view_id = self.env.ref("connector_ankorstore.view_aks_reject_wizard").id
        return {
            "name": _("Reject AKS Orders"),
            "view_mode": "form",
            "view_id": view_id,
            "res_model": "aks.reject.wizard",
            "type": "ir.actions.act_window",
            "target": "new",
        }

    def action_confirm(self):
        """Override the action_confirm method to check the availability of the order in the FC"""
        # internal order
        # Evaluate the change order lines
        # compare the qty ordered with the qty available in the FC
        # call transition to confirm the order
        # To accept the order as is, leave array empty.
        # To modify a single items quantity, include it with the new quantity value.
        # To remove an order item, specify its quantity as 0.
        # Assume don(t) do that : Some products are sold in fixed quantities, e.g a case of 6 wine bottles. To confirm a pack of 6 bottles pass a quantity of 1
        # _prepare_transition : return False ou dict
        for order in self:
            if order.aks_id:
                order._aks_internal_order_action_confirm()
            if order.aks_mode == "FFA" and not order.aks_id:
                if order.partner_shipping_id.check_aks_partner() and self.check_FC_availability():
                    # generate the uuid for the order it
                    order.aks_external_master_order = uuid.uuid4()
                    body = _("The order has been requested in the FC")
                    order.message_post(body=body)
            # next : create the external order in AKS
            res = super().action_confirm()
            if order.aks_mode == "FFA":
                picking = order.picking_ids[0]
                if not order.aks_id:
                    ret = picking.create_aks_picking()
                    if not ret:
                        raise ValidationError(_("Error while creating the external order in the FC"))
                else:
                    picking.aks_sync_status = "fulfillment_requested"
                    picking.action_assign()
        return res

    def _aks_internal_order_action_confirm(self):
        params = self.env["backend.aks"].get_params()
        if params.aks_active_fc and params.aks_auto_skip_reject:
            body = _("The order has been requested in the FC")
            self.message_post(body=body)
            return True
        transition_lines = self._prepare_transition()
        dict_to_send = {"data": {"type": "brand-validates", "attributes": {"orderItems": transition_lines}}}
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        url = self.env["backend.aks"].get_base_url()
        path = url + f"/api/v1/orders/{self.aks_id}/-actions/transition"
        try:
            # to avoid double confirm of exception after confirm
            if self.last_aks_status == "ankor_confirmed":
                session.http_request("POST", path, dict_to_send)
                body = _("The order has been confirmed to ankorstore")
            else:
                body = _("The order has already been confirmed to ankorstore")
            self.message_post(body=body)
            if self.aks_mode == "FFA":
                if params.aks_active_fc and not params.aks_auto_confirm_orders and self.last_aks_status != "fulfillment_requested":
                    time.sleep(2)  # wait avoid random http error 422
                    check = backend.make_api_request("GET", f"api/v1/orders/{self.aks_id}/is-fulfillable")
                    if check.status_code != 200:
                        raise ValidationError(_("The order is not fulfillable by the FC"))
                    body = _("The order is fulfillable by the FC")
                    self.message_post(body=body)
                    dict_to_send = {"data": {"type": "brand-requests-fulfillment"}}

                    path = url + f"/api/v1/orders/{self.aks_id}/-actions/transition"
                    session.http_request("POST", path, dict_to_send)
                body = _("The order has been requested in the FC")
                self.message_post(body=body)
                self.last_aks_status = "fulfillment_requested"
            return True
        except Exception as e:
            self.env["aks.sync.error"]._logme(
                model="sale.order", operation="_aks_internal_order_action_confirm", name=self.name, error_message=_("Error while confirming the order in the FC: %s" % e)
            )
            raise ValidationError(_("Error while confirming the order in the FC: %s" % e))
        #  https://www.public.ankorstore-sandbox.com/api/v1/orders/{order}/-actions/transition

    def check_FC_availability(self):
        """Check the availability of the order in the FC"""
        # TODO sanitize self.order_line in case product is in doublons in sale order line
        list_to_check = []
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()

        for line in self.order_line:

            # Skip non-product lines
            if line.product_id.type != "consu":
                continue
            if not line.product_id.is_aks_exported:
                raise ValidationError(_("The product is not exported to the FC : %s" % line.product_id.name))
            stock_id = line.product_id.get_aks_product()
            fulfillableid = stock_id.resource.fulfillableId
            if not fulfillableid:
                # Raise error if product is not available in FC
                raise ValidationError(_("The product is not available in the FC."))

            line.fulfillableid = fulfillableid
            str_filter = f"fulfillableIds[]={fulfillableid}"
            filter = Filter(str_filter)
            # https://www.public.ankorstore-sandbox,.com/api/v1/fulfillment/fulfillable?fulfillableIds[]=1eec3fa4-b6fc-617a-aac0-02f1999cea56
            try:
                fulfillments = session.get("fulfillment/fulfillable", filter)
            except Exception as e:
                self.env["aks.sync.error"]._logme(
                    model="sale.order",
                    operation="check_FC_availability",
                    name=self.name,
                    error_message=_("Error while checking the availability of the order in the FC: %s" % e),
                )
                raise ValidationError(_("Error while checking the availability of the order in the FC: %s" % e))

            list_to_check.append(
                {
                    "product": line.product_id.name,
                    "id": line.product_id.aks_id,
                    "asked_qty": line.product_uom_qty,
                    "fulfillableid": fulfillableid,
                    "free_qty": fulfillments.resource.unitQuantity,
                    "batchqty": fulfillments.resource.batchQuantity,
                    "packagingby": 0,
                    "check": False,
                }
            )

        # Evaluate availability for each line
        for line in list_to_check:
            self._evaluate_availability(line)

        # Return True if all lines are available
        if all([x["check"] for x in list_to_check]):
            return True
        error_list = [{"product": x["product"], "packagingby": x["packagingby"]} for x in list_to_check if not x["check"]]
        error_messages = []
        for error in error_list:
            error_messages.append(f"Product: {error['product']}, Waiting packaging by: {error['packagingby']}")
        error_message = "The following products are not available in the FC: " + ", ".join(error_messages) + ". Retry later or choose another route."

        raise ValidationError(_(error_message))

    def _evaluate_availability(self, line):
        """
        Evaluate the availability of the product in the line.
        """
        if line["batchqty"] > 0:
            if line["free_qty"] >= line["asked_qty"] and line["asked_qty"] % (line["free_qty"] / line["batchqty"]) == 0:
                line["check"] = True
            line["packagingby"] = line["free_qty"] / line["batchqty"]

    # INTERNAL ORDER PART

    def _prepare_transition(self):
        """Prepare the transition to confirm the order"""
        # To accept the order as is, leave the array empty.
        # To modify a single item's quantity, include it with the new quantity value.
        # To remove an order item, specify its quantity as 0.
        # we keep uom_qty,: assume dont do: Some products are sold in fixed quantities, e.g., a case of 6 wine bottles.
        # To confirm a pack of 6 bottles, pass a quantity of 1.
        orderitems = []
        # Create a dictionary of product IDs and quantities for order lines of type 'product'
        lines = dict(self.order_line.filtered(lambda x: x.product_id.type == "consu").mapped(lambda x: (x.orderitemid, x.product_uom_qty)))
        # Get the backend instance and session
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        include = backend.inclusion("billingItems", "retailer", "orderItems.productVariant.product")

        # Retrieve the order details from the external system
        try:
            order = session.get(f"orders/{self.aks_id}", include)
        except Exception as e:
            self.env["aks.sync.error"]._logme(
                model="sale.order", operation="_prepare_transition", name=self.name, error_message=_("Error while fetching the order details from the FC: %s" % e)
            )
            raise ValidationError(_("Error while fetching the order details from the FC: %s" % e))
        # update status in case of retry
        self.last_aks_status = order.resource.status
        # Create a dictionary of product variant IDs and quantities from the external order
        asked_lines = {x.id: x.multipliedQuantity for x in order.resource.orderItems}

        # Compare the quantity ordered with the quantity available in the FC
        for line in lines:
            if lines[line] != asked_lines.get(line, 0):
                orderitems.append({"id": line, "type": "order-items", "attributes": {"quantity": int(lines[line])}})

        # Check for lines in the external order that are not present in the internal order
        for line in asked_lines:
            if line not in lines:
                orderitems.append({"id": line, "type": "order-items", "attributes": {"quantity": 0}})

        return orderitems

    def action_list_aks_orders(self):
        """
        Fetches all orders from AKS and creates corresponding sale orders.

        This method communicates with the AKS platform to retrieve all
        existing orders, then iterates over these orders to create
        corresponding sale orders in Odoo using the `create_from_aks`.
        `create_from_aks` is normally triggered by webhook, using  action_list_aks_orders in cron watchdog
        """
        try:
            backend = self.env["backend.aks"].get_instance()
            session = backend.get_session()
            filter = Filter("filter[status]=ankor_confirmed")
            # Attempt to fetch all orders from AKS
            orders = session.get("orders", filter)
            # Ensure that orders were successfully fetched
            if not orders or not hasattr(orders, "resources"):
                self.env["aks.sync.error"]._logme(model="sale.order", operation="action_list_aks_orders", name="All", error_message=_("Failed to fetch orders from AKS."))
                _logger.error("Failed to fetch orders from AKS.")
                return False

            for order in orders.resources:
                try:
                    created_order = self.create_from_aks(order.id)
                    if not created_order:
                        _logger.warning(_(f"Failed to create a sale order for AKS order ID: {order.id}"))  # noqa
                except Exception as e:
                    _logger.error(f"Error creating sale order for AKS order ID {order.id}: {e}")  # noqa
                    continue

            return True
        except Exception as e:
            _logger.error(f"Unexpected error while listing AKS orders: {e}")
            self.env["aks.sync.error"]._logme(
                model="sale.order", operation="action_list_aks_orders", name="All", error_message=_("Unexpected error while listing AKS orders: %s" % e)
            )
            return False

    def set_status(self, aks_order_id, status):
        """Set the status of the order internal ordera nd propagate to the picking if FFA mode"""
        so = self.search([("aks_id", "=", aks_order_id)])
        if so:
            so.last_aks_status = status
            body = _("Status of the order has been set to %s", status)
            so.message_post(body=body)
            if so.aks_mode == "FFA":
                for picking in so.picking_ids:
                    picking.aks_sync_status = status
                    if so.last_aks_status == "fulfillment_requested":
                        if picking.state != "assigned":
                            picking.action_assign()
                        picking.message_post(body=body)
            if so.last_aks_status == "cancelled":
                if so.state not in ["cancel", "done"]:
                    so.with_context(disable_cancel_warning=True).action_cancel()
            if so.last_aks_status == "shipped":
                if so.aks_mode == "FFA":
                    for picking in so.picking_ids.filtered(lambda x: x.state not in ["done", "cancel"]):
                        if picking.state != "assigned":
                            picking.action_assign()
                        fullfilments_ids = picking.get_fullfiment_ids(so.aks_internal_master_order)
                        if fullfilments_ids:
                            picking.get_fulfillment_status(fullfilments_ids[0])
                        picking.with_context(cancel_backorder=True, skip_sms=True).button_validate()
                        picking.message_post(body=body)
                        picking.get_shipping_overview()
            if so.last_aks_status in ["received", "invoiced", "brand_paid"]:
                if so.aks_mode == "FFA":
                    for picking in so.picking_ids.filtered(lambda x: x.state not in ["done", "cancel"]):
                        picking.button_validate()
                        picking.message_post(body=body)
                        picking.get_shipping_overview()

    def get_last_status(self):
        """Get the last status of the order for internal order"""
        self.ensure_one()
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        path = f"orders/{self.aks_id}"
        order = session.get(path)
        self.set_status(self.aks_id, order.resource.status)

    def cron_get_last_status(self):
        """Get the last status of the order for internal order"""
        for backend in self.env["backend.aks"].search([]):
            orders = self.search([("aks_id", "!=", False), ("aks_mode", "=", "FFA"), ("company_id", "=", backend.company_id.id)])
            for order in orders:
                for picking in order.picking_ids.filtered(lambda x: x.state not in ["done", "cancel"]):
                    order.with_context(default_company_id=backend.company_id.id).get_last_status()
            orders = self.search([("aks_external_master_order", "!=", False), ("company_id", "=", backend.company_id.id)])
            for order in orders:
                for picking in order.picking_ids.filtered(lambda x: x.state not in ["done", "cancel"]):
                    picking.with_context(default_company_id=backend.company_id.id).get_last_status()

    def create_from_aks(self, aks_order_id):
        """
        Create an Odoo sale order from an AKS order.

        This method is typically triggered by a webhook or a queue job to
        create a sale order in Odoo based on the details fetched from an
        AKS order identified by its ID. It handles the creation of customer
        and shipping information, order lines, and associated fees.
        Triggered by webhook
        """
        # Initialize a list to hold sale order line data

        sale_order_lines = []
        # Check if the order already exists
        sale_order = self.search([("aks_id", "=", aks_order_id)])
        if sale_order:
            return sale_order
        try:
            backend = self.env["backend.aks"].get_instance()
            session = backend.get_session()

            # fetch one order from AKS including all informations
            include = backend.inclusion("retailer", "billingItems", "orderItems.productVariant.product")  # TO FIX JSON:API : the first arg is not used ?
            aks_order = session.get(f"orders/{aks_order_id}", include)

            if not aks_order:
                return False
            # split objetcs

            order = aks_order.resource
            partner_retailer = order.retailer  # Customer
            billing_items = order.billingItems  # AKS Fees or refund
            order_items = order.orderItems  # Products

            # Search for backend.aks parameters based on the current company
            params = self.env["backend.aks"].get_params()

            # Prepare order lines based on the fetched order items
            sale_order_lines = self._prepare_order_line(order_items)

            # Prepare partner and shipping information
            partner_id = self._prepare_partner(partner_retailer, order).id
            partner_invoice_id = self._prepare_billing_partner(order, partner_id, params).id
            partner_shipping_id = self._prepare_shipping_partner(order, partner_id).id  # noqa

            # Prepare AKS fees information
            order_fees = self._prepare_aks_fees(billing_items)

            # Create the sale order in Odoo with all the gathered information

            warehouse_id = self._get_warehouse_available()
            if params.aks_active_fc:
                if params.aks_auto_confirm_orders:
                    warehouse_id = self.env["stock.warehouse"].search([("is_aks_fc", "=", True), ("company_id", "=", params.company_id.id)], limit=1).id

            partner = self.env["res.partner"].browse(partner_id)
            if partner.user_id:
                user_id = partner.user_id.id
            else:
                user_id = params.res_users_id.id

            enable_delivery_partner = True
            sale_order = (
                self.env["sale.order"]
                .with_context(mail_create_nosubscribe=True)
                .sudo()
                .create(
                    {
                        "partner_id": partner_id,
                        "partner_invoice_id": partner_invoice_id if enable_delivery_partner else partner_id,
                        "partner_shipping_id": partner_shipping_id if enable_delivery_partner else partner_id,
                        "aks_id": order.id,
                        "aks_internal_master_order": order.masterOrderId if order.masterOrderId else False,
                        "aks_last_synchronization": datetime.now(),
                        "last_aks_status": order.status,
                        "client_order_ref": order.reference,
                        "date_order": self._convert_aks_date(order.submittedAt),
                        "team_id": params.sale_order_default_team.id,
                        "commitment_date": self._convert_aks_date(order.shippingOverview.expectedShippingDates.maximum),
                        "order_line": sale_order_lines,
                        "aks_fees": order_fees,
                        "aks_mode": "FFA" if params.aks_active_fc and params.aks_auto_confirm_orders else "FFB",
                        "warehouse_id": warehouse_id,
                        "user_id": user_id,
                        "company_id": params.company_id.id,
                    }
                )
            )

            followers = sale_order.message_follower_ids
            partner_ids = followers.mapped("partner_id")
            sale_order.message_unsubscribe(partner_ids.ids)
            sale_order.message_subscribe(partner_ids=self.env["backend.aks"].get_followers())
            body = _("The order has been created in Odoo :%s", sale_order.aks_id)
            sale_order.message_post(body=body)
            if partner_id != partner_shipping_id and not enable_delivery_partner:
                body = _("ALERT ! The delivery address seems to be different with the founded partner (vat or mail) !, please check it.")
                sale_order.message_post(body=body)
            shipping_partner = self.env["res.partner"].browse(partner_shipping_id)
            if any(value in (False, None, "") for value in (shipping_partner.city, shipping_partner.street, shipping_partner.zip)):
                body = _("ALERT ! Missing required fields for partner shipping address (street, postalCode, countryCode, city).\n Fill those required fields to make delivery process possible.")
                sale_order.message_post(body=body)
                self.env["aks.sync.error"]._logme(model="sale.order", operation="partner_shipping_values_checkup", name=order.reference, error_message=body)
            if params.aks_auto_skip_reject:
                sale_order.action_confirm()
            return sale_order
        except Exception as e:
            self.env["aks.sync.error"]._logme(
                model="sale.order", operation="create_from_aks", name=order.reference, error_message=_("Error while creating the order in Odoo: %s" % e)
            )
            return False

    def _get_warehouse_available(self):
        #  self.env['ir.default'].sudo()._default_get('sale.order', 'warehouse_id', company_id= self.env.company.id) or
        return (
            self.env["backend.aks"].get_params().aks_default_warehouse_id.id
            or self.env["ir.default"]._get_model_defaults("sale.order").get("warehouse_id")
            or self.user_id.with_company(self.company_id.id)._get_default_warehouse_id().id
        )

    def _prepare_order_line(self, order_items):
        """
        Prepare sale order lines from AKS order items.

        This method iterates over each item in the order, searches for the
        corresponding product in Odoo by the AKS product variant ID,
        and prepares order line data including the product, quantity,
        and unit price. Taxes (VAT) are determined based on the product's
        tax rate.
        """
        sale_order_line = []
        for item in order_items:
            product = self.env["product.product"].search([("aks_id", "=", item.productVariant.id)], limit=1)
            if not product:
                product = self.env["product.product"].search([("default_code", "=", item.productVariant.sku)], limit=1)
            if not product:
                # TODO: raise exception
                _logger.warning(_(f"Product with AKS ID {item.productVariant.id} not found."))  # noqa
                # to remove in prod !
                product = self.env["product.product"].create(
                    {
                        "name": item.productVariant.product.name,
                        "aks_id": item.productVariant.id,
                        "type": "consu",
                    }
                )
            sale_order_line.append(
                (
                    0,
                    0,
                    {
                        "product_id": product.id,
                        "product_uom_qty": item.multipliedQuantity,  # item.quantity
                        "price_unit": item.brandUnitPrice / 100,
                        "orderitemid": item.id,
                    },
                )
            )

        return sale_order_line

    def _prepare_aks_fees(self, billing_items):
        """Store AKS fees as temp in Odoo"""
        ask_fees_line = []
        params = self.env["backend.aks"].get_params()
        for item in billing_items:
            # brand_shipping_fees
            # ankorstore_fees,
            product = params.aks_fees_product
            if not product:
                raise ValidationError(_("The product for AKS fees is not defined"))
            ask_fees_line.append(
                (
                    0,
                    0,
                    {
                        "type": item.fields.type,  # use proxy to find the good type (!= Type)
                        "product_id": product.id,  # "product_id": item.productId,
                        "currency": item.currency,
                        "vatRate": item.vatRate,
                        "currencyRate": item.currencyRate,
                        "amount": -item.amount / 100,
                        "amountVat": -item.amountVat / 100,
                        "amountWithVat": -item.amountWithVat / 100,
                    },
                )
            )
        return ask_fees_line

    def _prepare_partner(self, retailer, order):
        """
        Prepare and return a partner based on retailer information.

        Search for an existing partner using the retailer's ID.
        If found, update existing partner with the latest information.
        If not found, create a new partner.
        """
        partner = False
        if retailer.vatNumber:
            partner = self.env["res.partner"].search([("vat", "=", retailer.vatNumber)], limit=1)
        if not partner and retailer.email:
            partner = self.env["res.partner"].search([("email", "=", retailer.email), ("is_company", "=", True)], limit=1)
        country = self.env["res.country"].search([("code", "=", order["billingCountryCode"])], limit=1)
        partner_vals = {
            "aks_retailer_id": retailer.id,
            "name": retailer.companyName or retailer.storeName or retailer.lastName,
            "aks_store_name": retailer.storeName,
            "email": retailer.email,
            "vat": retailer.vatNumber,  # TODO: must be valid in odoo
            "phone": retailer.phoneNumberE164,
            "is_company": True,
            "street": order["billingStreet"],
            "zip": order["billingPostalCode"],
            "city": order["billingCity"],
            "country_id": country.id,
        }
        if not partner:
            # Create a new partner if none was found
            Partner = self.env["res.partner"]
            ret = Partner.simple_vat_check(country.code, retailer.vatNumber)
            if not ret:
                partner_vals.pop("vat")
            partner = Partner.create(partner_vals)
        return partner

    def _prepare_billing_partner(self, order, parent_id, params):
        """
        Prepare and return the billing partner for a given order.
        """
        if params.is_aks_invoicing_partner:
            if params.aks_partner:
                return params.aks_partner
            else:
                return self.browse(parent_id)
        else:
            return self.browse(parent_id)

    def _prepare_shipping_partner(self, order, parent_id):
        """
        Prepare and return the shipping partner for a given order.

        If a partner with the same name exists but has a different address,
        a new address is created under this partner.
        If the address matches an existing one, that address is reused.
        Otherwise, a new partner with the provided address is created.
        """
        # {
        #     'shipToAddress': {
        #         'name': 'Simon',
        #         'organisationName': 'SAS ERE EDOX',
        #         'street': 'Dummy Street',
        #         'city': 'BORDEAUX',
        #         'postalCode': '33000',
        #         'countryCode': 'FR'
        #     }
        # }
        shipping_address = order["shippingOverview"]["shipToAddress"]

        # First, try to find a partner by name (organisationName)
        # regardless of the address
        partner = self.env["res.partner"].search([("id", "=", parent_id)], limit=1)

        if any(value in (False, None, "") for value in (shipping_address.get("street", ""), shipping_address.get("postalCode", ""), shipping_address.get("countryCode", ""), shipping_address.get("city", ""))):
            if partner: # Ensure no "dumb" error -> no logs in sale_order tchatter available because created later in workflow
                partner.message_post(body=_("WARNING: Missing required fields for partner shipping address (street, postalCode, countryCode, city).\n Field those required fields to make delivery process possible."))
                self.env["aks.sync.error"]._logme(model="sale.order", operation="_prepare_shipping_partner", name=order.reference, 
                    error_message=_("WARNING: Missing required fields for partner shipping address (street, postalCode, countryCode, city).\n Field those required fields to make delivery process possible."))

        country = self.env["res.country"].search([("code", "=", shipping_address["countryCode"])], limit=1)

        # Check if an existing delivery address matches the current one
        if partner:
            partner_shipping = self.env["res.partner"].search(
                [
                    ("parent_id", "=", parent_id),
                    ("type", "=", "delivery"),
                    ("street", "=", shipping_address["street"]),
                    ("zip", "=", shipping_address["postalCode"]),
                    ("city", "=", shipping_address["city"]),
                    ("country_id", "=", country.id),
                ],
                limit=1,
            )

            # If no matching delivery address is found,
            # create a new one under the found partner
            if not partner_shipping:
                partner_shipping = self.env["res.partner"].create(
                    {
                        "parent_id": parent_id,
                        "type": "delivery",
                        "name": shipping_address.get("name", partner.name),
                        "city": shipping_address["city"],
                        "street": shipping_address["street"],
                        "zip": shipping_address["postalCode"],
                        "country_id": country.id,
                    }
                )

        else:
            # If no partner is found by name,
            # create a new main partner and delivery address
            partner_shipping = self.env["res.partner"].create(
                {
                    "parent_id": parent_id,
                    "type": "delivery",
                    "name": shipping_address["organisationName"],
                    "city": shipping_address["city"],
                    "street": shipping_address["street"],
                    "zip": shipping_address["postalCode"],
                    "country_id": country.id,
                }
            )
        return partner_shipping

    def action_confirm_delivery_quote(self):
        """
        Confirm the delivery quote for the sale order.
        """
        self.ensure_one()
        if self.aks_latest_quote:
            for picking in self.picking_ids:
                params = self.env["backend.aks"].get_params()
                if params.aks_auto_confirm_quote:
                    carrier = self.env["delivery.carrier"].search([("delivery_type", "=", "aks")], limit=1)
                    if carrier:
                        ret = carrier.aks_send_shipping(picking)
                        if ret:
                            picking.message_post(body=_("The delivery quote has been confirmed."))
                            self.message_post(body=_("The delivery quote has been confirmed."))

    @api.model
    def _convert_aks_date(self, date):
        """Convert AKS date to odoo date
        date format : 2021-03-01T10:00:00+01:00"""

        if not date:
            return False
        datetime_object = datetime.strptime(date, "%Y-%m-%dT%H:%M:%S%z")
        formatted_time = datetime_object.strftime("%Y-%m-%d %H:%M:%S")
        return formatted_time


class AksFeesLine(models.Model):
    """
    This model represents a line of fees related to an Ankorstore order.
    Each line includes details such as the type of fee, the currency, VAT rate, currency rate,
    and amounts (with and without VAT).

    _summary_AksFeesLine model description_
    {'type': 'billing-items', 'id': '1ee221eb-ee39-6a06-9666-5a22ec669afe', 'attributes': {'type': 'ankorstore_fees', 'currency': 'EUR', 'vatRate': 20, 'currencyRate': 1, 'amount': -2819, 'amountVat': -564, 'amountWithVat': -3383, 'createdAt': '2023-07-14T08:16:34+00:00', 'updatedAt': '2024-01-30T18:21:52+00:00'}},
    {'type': 'billing-items', 'id': '1eebf9c7-1238-6e40-98e2-0e8a0830fa35', 'attributes': {'type': 'brand_shipping_fees', 'currency': 'EUR', 'vatRate': 0, 'currencyRate': 1, 'amount': -1200, 'amountVat': 0, 'amountWithVat': -1200, 'createdAt': '2024-01-30T18:21:52+00:00', 'updatedAt': '2024-01-30T18:21:52+00:00'}}
    """

    _name = "aks.fees.line"
    _description = "Ankorstore Fees"
    _rec_name = "type"
    _order = "type"

    order_id = fields.Many2one("sale.order", string="Order")
    type = fields.Char(string="Type")
    # map to product.product services only
    product_id = fields.Many2one("product.product", string="Product", domain=[("type", "=", "service")])
    currency = fields.Char(string="Currency")
    vatRate = fields.Float(string="VAT Rate")
    currencyRate = fields.Float(string="Currency Rate")
    amount = fields.Float(string="Amount")
    amountVat = fields.Float(string="Amount VAT")
    amountWithVat = fields.Float(string="Amount with VAT")
