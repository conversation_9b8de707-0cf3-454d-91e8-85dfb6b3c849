<odoo>
    <data noupdate="1">
        <!-- Predefined webhook subscription for 'Brand Order Created' -->
        <record id="backend_aks_webhook_order_brand_created" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.brand_created</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Brand Order Accepted' -->
        <record id="backend_aks_webhook_order_brand_accepted" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.brand_accepted</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Brand Order Rejected' -->
        <record id="backend_aks_webhook_order_brand_rejected" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.brand_rejected</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Billing Address Updated' -->
        <record id="backend_aks_webhook_order_billing_address_updated" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.billing_address_updated</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Shipping Address Updated' -->
        <record id="backend_aks_webhook_order_shipping_address_updated" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipping_address_updated</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Shipping Labels Generated' -->
        <record id="backend_aks_webhook_order_labels_generated" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.labels_generated</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>
        <!-- Predefined webhook subscription for 'Order Shipped' -->
        <record id="backend_aks_webhook_order_shipped" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipped</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Shipment Received' -->
        <record id="backend_aks_webhook_order_shipment_received" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipment_received</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Shipment Refused' -->
        <record id="backend_aks_webhook_order_shipment_refused" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipment_refused</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Brand Paid' -->
        <record id="backend_aks_webhook_order_brand_paid" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.brand_paid</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Order Cancelled' -->
        <record id="backend_aks_webhook_order_cancelled" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.cancelled</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Brand Accepted Reverted' -->
        <record id="backend_aks_webhook_order_brand_accepted_reverted" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.brand_accepted_reverted</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'Shipping Labels Generated Reverted' -->
        <record id="backend_aks_webhook_order_shipping_labels_generated_reverted" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipping_labels_generated_reverted</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'External Order Created' -->
        <record id="backend_aks_webhook_order_created" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.created</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'External Order Awaiting Fulfillment' -->
        <record id="backend_aks_webhook_order_awaiting_fulfillment" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.awaiting_fulfillment</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'External Order Cancelled' -->
        <record id="backend_aks_webhook_order_cancelled" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.cancelled</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'External Order Shipped' -->
        <record id="backend_aks_webhook_order_shipped" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.shipped</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

        <!-- Predefined webhook subscription for 'External Order Arrived' -->
        <record id="backend_aks_webhook_order_arrived" model="backend.aks.webhook">
            <field name="backend_id" ref="backend.aks.webhook"/>
            <field name="event">order.arrived</field>
            <field name="url">https://your-odoo-instance.com/ankorstore/webhook</field>
            <field name="active" eval="True"/>
            <field name="secret_key">YourSecretKeyHere</field>
        </record>

    </data>
</odoo>