# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, exceptions, _
import logging


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    fulfillableid = fields.Char(string='Fulfilment ID',
                                help='Fulfilment ID from Ankorstore',
                                copy=False)
    orderitemid = fields.Char(string='Order Item ID',
                              help='Order Item ID from Ankorstore',
                              copy=False)

    def _prepare_procurement_values(self, group_id=False):
        """ Prepare specific key for moves or other components that will be created from a stock rule
        coming from a sale order line. This method could be override in order to add other custom key that could
        be used in move/po creation.
        """
        res = super(SaleOrderLine, self)._prepare_procurement_values(group_id=group_id)
        res['fulfillableid'] = self.fulfillableid
        return res