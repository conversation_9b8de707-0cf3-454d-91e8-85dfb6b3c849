from odoo import models, fields, api, _
from odoo.exceptions import UserError


class ShippingInfoWizard(models.TransientModel):
    _name = 'shipping.info.wizard'
    _description = 'Wizard for Entering Shipping Information'

    tracking_url = fields.Char('Tracking URL', help='Enter the tracking URL of the shipment.')
    package_line_ids = fields.One2many('shipping.package.line', 'wizard_id', string='Packages')

    @api.model
    def default_get(self, fields):
        res = super(ShippingInfoWizard, self).default_get(fields)
        res["tracking_url"] = self.env['ir.config_parameter'].sudo().get_param('connector_ankorstore.default_tracking_url') or False
        return res

    def action_confirm(self):
        self.ensure_one()
        rec = self.env['stock.picking'].browse(self._context.get('active_id'))
        if not self.package_line_ids:
            raise UserError(_("Please add at least one package to confirm the shipping."))
        if self.package_line_ids:
            parcels = []
            for package in self.package_line_ids:
                for seq in range(package.parcel_number):
                    parcels.append({
                        "length": int(package.package_id.packaging_length/10),
                        "width": int(package.package_id.width/10),
                        "height": int(package.package_id.height/10),
                        "distanceUnit": "cm",
                        "weight": package.parcel_weight*1000,
                        "massUnit": "g",
                    })

                if parcels:
                    payload = {
                            "shipping": {"parcels": parcels}
                        }
                    backend = self.env["backend.aks"].get_instance()
                    session = backend.get_session()
                    url = self.env["backend.aks"].get_base_url()
                    path = url + f"/api/v1/orders/{rec.aks_id}/ship/custom"
                    quotes = session.http_request('POST', path, payload)
                    data = quotes[1]['data']
                    quote = data["attributes"]["shippingOverview"]["latestQuote"]["id"]
                    path = url + f"/api/v1/shipping-quotes/{quote}/confirm"
                    if self.tracking_url:
                        payload = {"tracking": {
                                    "trackingLink": self.tracking_url
                                }}
                    else:
                        payload = {}
                    try:
                        ret = session.http_request('POST', path, payload)
                        rec.aks_confirm_custom_ship_done = True
                        rec.message_post(body=f"Custom Shipping information sent to Ankorstore API. Tracking URL: {self.tracking_url}")
                    except Exception as e:
                        error_message = f"Error while sending shipping request to Ankorstore API: {e}"
                        self.env['aks.sync.error']._logme(
                            model='stock.picking',
                            operation='button_aks_confirm_custom_ship',
                            name=rec.name,
                            error_message=error_message)
                        raise UserError(_(f"Error while sending shipping request to Ankorstore API: {e}"))
        return {'type': 'ir.actions.act_window_close'}


class ShippingPackageLine(models.TransientModel):
    _name = 'shipping.package.line'
    _description = 'Package Line'

    wizard_id = fields.Many2one('shipping.info.wizard', string='Wizard', ondelete='cascade', readonly=True)
    package_id = fields.Many2one("stock.package.type", string="Package Type")
    parcel_number = fields.Integer(string="Parcel Number", default=1)
    parcel_weight = fields.Float(string="Weight by parcel(Kg)")
