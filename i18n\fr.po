# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* connector_ankorstore
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e-20241007\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-03 08:58+0000\n"
"PO-Revision-Date: 2025-04-03 08:58+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"/!\\ Check “Autoassign Ankorstore orders to FC” only if you are sure that "
"your setting in Ankorstore is similar. If you are part of the "
"AnkorsLogistics Fulfilment Programme, reflect here whether you want your "
"Ankorstore orders to be automatically assigned to the Fulfilment Center, "
"when the order is validated"
msgstr ""
"/!\\ Cochez “Autoassigner les commandes Ankorstore du centre de "
"distributionuniquement si vous êtes sûr que votre paramétrage dans "
"Ankorstore est similaire.Si vous faites partie du programme de traitement "
"des commandes d'AnkorsLogisticsindiquez ici si vous souhaitez que vos "
"commandes Ankorstore soient automatiquementassignées du centre de "
"distribution, quand la commande est validé"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"/!\\ Check “Autoconfirm Ankorstore orders” only if you are sure that your "
"setting in Ankorstore is similar. If you are part of the AnkorsLogistics "
"Fulfilment Programme, reflect here whether you want your Ankorstore orders "
"to be automatically accepted when they are imported from Ankorstore"
msgstr ""
"/!\\ Cochez « Autoconfirmer les commandes Ankorstore » uniquement si vous "
"êtes sûr que votre paramétrage dans Ankorstore est similaire. Si vous faites"
" partie du programme de traitement des commandes d'AnkorsLogistics, indiquez"
" ici si vous souhaitez que vos commandes Ankorstore soient automatiquement "
"acceptées lorsqu'elles sont importées depuis Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"/!\\ Important: You must use the same name as the application name used when"
" created in Ankorstore. Otherwise, systems will not be able to communicate"
msgstr ""
"/!\\ Important : Vous devez utiliser le même nom que le nom de l'application"
" utilisé lors de sa création dans Ankorstore. Sinon, les systèmes ne "
"pourront pas communiquer"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__created
msgid "A new external order is created from the brand"
msgstr "Une nouvelle commande externe est créée par la marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__ankor_confirmed
msgid "A new internal order is created for the brand"
msgstr "Une nouvelle commande interne est créée pour la marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__delivery_carrier__aks_label_stock_type__a4
msgid "A4"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_package_type__package_carrier_type__aks
msgid "AKS"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_archived_at
msgid "AKS Archived At"
msgstr "Archivé par AKS le"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_available_quantity
msgid "AKS Available Quantity"
msgstr "Quantité disponible sur AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_confirm_custom_ship_done
msgid "AKS Confirm Custom Ship Done"
msgstr "AKS confirme Expédition personnalisée"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_stock_picking_form_inherit
msgid "AKS Confirm Shippping"
msgstr "Confirmer l'expédition AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_created_at
msgid "AKS Created At"
msgstr "Créé par AKS le"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_latest_quote
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_latest_quote
msgid "AKS Delivery Latest Quote"
msgstr "Dernier devis de livraison AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_external_order
msgid "AKS External Order"
msgstr "Commande externe AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_fees
msgid "AKS Fees"
msgstr "Frais AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_fulfillable_id
msgid "AKS Fullilable ID"
msgstr "AKS Fulfillable ID"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_ian
msgid "AKS IAN"
msgstr "IAN AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_id
msgid "AKS Internal Order ID"
msgstr "ID de commande interne AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__is_active_on_aks
msgid "AKS Is Active"
msgstr "Actif sur AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__is_aks_always_in_stock
msgid "AKS Is Always In Stock"
msgstr "Toujours en stock sur AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_last_synchronization
msgid "AKS Last Synchronization"
msgstr "Dernière synchronisation AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_master_order
msgid "AKS Master Order"
msgstr "Commande maître AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_name
msgid "AKS Name"
msgstr "Nom AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_id
msgid "AKS Order UUID"
msgstr "UUID de commande AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_original_wholesale_price
msgid "AKS Original Wholesale Price"
msgstr "Prix de gros original AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_product_master_id
msgid "AKS Product Master ID"
msgstr "ID du produit maître AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__aks_reject_reason
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_reject_reason
msgid "AKS Reject Reason"
msgstr "Raison de rejet AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_reserved_quantity
msgid "AKS Reserved Quantity"
msgstr "Quantité réservée sur AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_retail_price
msgid "AKS Retail Price"
msgstr "Prix de détail AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_res_partner__aks_retailer_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_res_users__aks_retailer_id
msgid "AKS Retailer ID"
msgstr "ID du commerçant AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_sku
msgid "AKS SKU"
msgstr "SKU AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_stock_quantity
msgid "AKS Stock Quantity"
msgstr "Quantité en stock sur AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_res_partner__aks_store_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_res_users__aks_store_name
msgid "AKS Store Name"
msgstr "Nom du magasin AKS"

#. module: connector_ankorstore
#: model:ir.actions.act_window,name:connector_ankorstore.action_aks_sync_error_tree
msgid "AKS Sync Errors"
msgstr "Erreurs de synchronisation AKS"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_sync_error
msgid "AKS Synchronization Errors"
msgstr "Erreurs de synchronisation AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_sync_status
msgid "AKS Synchronization Status"
msgstr "Statut de synchronisation AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__aks_tracking_link
msgid "AKS Tracking Link"
msgstr "Lien de suivi AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_external_master_order
msgid "AKS UUID External Order"
msgstr "UUID de commande externe AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_updated_at
msgid "AKS Updated At"
msgstr "Mis à jour par AKS le"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_wholesale_price
msgid "AKS Wholesale Price"
msgstr "Prix de gros AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_needaction
msgid "Action Needed"
msgstr "Action requise"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Activate connection"
msgstr "Activer la connexion"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__active
msgid "Active"
msgstr "Actif"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Décoration d'exception d'activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_state
msgid "Activity State"
msgstr "État de l'activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.delivery_carrier_form_view_inherit
msgid "Aks Default Package Type"
msgstr "Type d'emballage par défaut AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_delivery_carrier__aks_label_format
msgid "Aks Label Format"
msgstr "Format d'étiquette AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_delivery_carrier__aks_label_stock_type
msgid "Aks Label Stock Type"
msgstr "Type d'étiquette AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_sync_allowed_location_ids
msgid "Allowed Stock Locations to sync in AKS"
msgstr "Emplacements de stock autorisés à synchroniser dans AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__amount
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__amount
msgid "Amount"
msgstr "Montant"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__amountVat
msgid "Amount VAT"
msgstr "Montant TVA"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__amountWithVat
msgid "Amount with VAT"
msgstr "Montant avec TVA"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__delivery_carrier__delivery_type__aks
#: model:ir.ui.menu,name:connector_ankorstore.aks_menu
#: model:ir.ui.menu,name:connector_ankorstore.menu_root_aks
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.product_normal_form_view_aks
msgid "Ankorstore"
msgstr ""

#. module: connector_ankorstore
#: model:ir.actions.act_window,name:connector_ankorstore.action_backend_aks_form
#: model:ir.model,name:connector_ankorstore.model_backend_aks
msgid "Ankorstore Backend"
msgstr "Backend Ankorstore"

#. module: connector_ankorstore
#: model:ir.actions.act_window,name:connector_ankorstore.action_backend_aks_tree
msgid "Ankorstore Backends"
msgstr "Backends Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_partner
msgid "Ankorstore Billing Partner"
msgstr "Partenaire de facturation Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.res_config_settings_view_form_connector_ankorstore
msgid "Ankorstore Connector"
msgstr "Connecteur Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.constraint,message:connector_ankorstore.constraint_sale_order_aks_external_master_order_uniq
msgid "Ankorstore External order UUID must be unique"
msgstr "L'UUID de commande externe Ankorstore doit être unique"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_fees_line
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_order_form_aks
msgid "Ankorstore Fees"
msgstr "Frais Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Ankorstore Fulfillment Center (FC)"
msgstr "fulfilment centre Ankorstore (FC)"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_internal_master_order
msgid "Ankorstore Internal Master order UUID"
msgstr "UUID de commande maître interne Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.constraint,message:connector_ankorstore.constraint_sale_order_aks_internal_master_order_uniq
msgid "Ankorstore Internal Master order UUID must be unique"
msgstr "L'UUID de commande maître interne Ankorstore doit être unique"

#. module: connector_ankorstore
#: model:ir.model.constraint,message:connector_ankorstore.constraint_sale_order_aks_id_uniq
msgid "Ankorstore Order UUID must be unique"
msgstr "L'UUID de commande Ankorstore doit être unique"

#. module: connector_ankorstore
#: model:ir.ui.menu,name:connector_ankorstore.menu_setup
msgid "Ankorstore Setup"
msgstr "Configuration Ankorstore"

#. module: connector_ankorstore
#: model:ir.actions.server,name:connector_ankorstore.aks_sync_stock_from_odoo_ir_actions_server
msgid "Ankorstore Sync AKS Stock Level From Odoo"
msgstr "Synchronisation du stock AKS depuis Odoo"

#. module: connector_ankorstore
#: model:ir.actions.server,name:connector_ankorstore.aks_sync_orders_status_ir_actions_server
msgid "Ankorstore Sync All Orders Status"
msgstr "Status de synchronisation des commandes Ankorstore"

#. module: connector_ankorstore
#: model:ir.actions.server,name:connector_ankorstore.aks_sync_catalog_ir_actions_server
msgid "Ankorstore Sync Catalog"
msgstr "Synchronisation du catalogue Ankorstore"

#. module: connector_ankorstore
#: model:ir.ui.menu,name:connector_ankorstore.menu_log
msgid "Ankorstore Sync Errors"
msgstr "Erreurs de synchronisation Ankorstore"

#. module: connector_ankorstore
#: model:ir.actions.server,name:connector_ankorstore.aks_sync_stock_from_aks_fc_ir_actions_server
msgid "Ankorstore Sync Odoo Stock Level From AKS FC"
msgstr "Synchronisation du stock Odoo depuis AKS FC"

#. module: connector_ankorstore
#: model:ir.actions.server,name:connector_ankorstore.aks_sync_orders_ir_actions_server
msgid "Ankorstore Sync Orders From AKS FC"
msgstr "Synchroniser les commandes depuis l'FC AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__aks_id
msgid "Ankorstore UUID"
msgstr "UUID Ankorstore"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_backend_aks_webhook
msgid "Ankorstore Webhook Subscription"
msgstr "Abonnement aux webhooks Ankorstore"

#. module: connector_ankorstore
#: model:ir.actions.act_window,name:connector_ankorstore.action_backend_aks_webhook_tree
msgid "Ankorstore Webhooks"
msgstr "Webhooks Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Ankorstore as default invoicing partner?"
msgstr "Choisir Ankorstore comme partenaire de facturation par défaut ?"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Ankorstore fees"
msgstr "Frais Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_app_id
msgid "Application ID"
msgstr "ID de l'application"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_app_name
msgid "Application Name"
msgstr "Nom de l'application"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__display_variants_exported
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_template__display_variants_exported
msgid "Are all the variants exported to AKS"
msgstr "Tous les variantes sont-ils exportés vers AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__args
msgid "Args"
msgstr ""

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Assign a dedicated salesperson to all Ankorstore orders, in case there is no"
" salesperson already assigned to the customer account"
msgstr ""
"Affecter un commercial dédié à toutes les commandes Ankorstore, au cas où "
"aucun commercial n'est déjà affecté au compte client"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__res_users_id
msgid "Assigned Salesperson"
msgstr "Commercial assigné"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_auto_confirm_quote
msgid "Auto Confirm Quote"
msgstr "Confirmation Devis Auto"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_auto_skip_reject
msgid "Auto confirm Ankorstore Orders"
msgstr "Confirmation automatique des commandes Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Autoassign Ankorstore orders to FC"
msgstr "Autoassigner les commandes Ankorstore au FC"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Autoconfirm Ankorstore orders"
msgstr "Autoconfirmer les commandes Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__choose_delivery_carrier__calculate_parcel_method__automatic
msgid "Automatic"
msgstr "Automatique"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_auto_confirm_quote
msgid "Automatically confirm quotes in Ankorstore."
msgstr "Confirmer automatiquement les devis dans Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_auto_confirm_orders
msgid "Automatically route by Ankorstore FC"
msgstr "Routage automatique par le FC Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__backend_id
msgid "Backend"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__odoo_webhook_base_url
msgid "Base Odoo URL for Webhooks"
msgstr "URL de base Odoo pour les webhooks"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__base_url_prod
msgid "Base URL PRODUCTION"
msgstr "URL PRODUCTION par défaut"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__base_url_dev
msgid "Base URL STAGING"
msgstr "URL STAGING par défaut"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_billing_address_updated
msgid "Billing Address Updated"
msgstr "Adresse de facturation mise à jour"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_brand_accepted_reverted
msgid "Brand Accepted Reverted"
msgstr "Marque acceptée annulée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_brand_accepted
msgid "Brand Order Accepted"
msgstr "Commande Acceptée par la Marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_brand_created
msgid "Brand Order Created"
msgstr "Commande Crée par la Marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_brand_rejected
msgid "Brand Order Rejected"
msgstr "Commande Rejetée par la Marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_brand_paid
msgid "Brand Paid"
msgstr "La marque a payé"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__brand_already_has_customer_in_the_area
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__brand_already_has_customer_in_the_area
msgid "Brand already has customer in the area"
msgstr "La marque possède déjà un client dans la région"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__brand_cannot_deliver_to_the_area
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__brand_cannot_deliver_to_the_area
msgid "Brand cannot deliver to the area"
msgstr "La marque ne peut pas livrer dans la région"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__brand_confirmed
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__brand_confirmed
msgid "Brand confirmed the order"
msgstr "la marque a confirmé la commande"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__brand_has_exclusive_distributor_in_the_region
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__brand_has_exclusive_distributor_in_the_region
msgid "Brand has exclusive distributor in the region"
msgstr "La marque a un distributeur exclusif dans la région"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__brand_paid
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__brand_paid
msgid "Brand paid"
msgstr "la marque a payé"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__buyer_not_a_retailer
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__buyer_not_a_retailer
msgid "Buyer not a retailer"
msgstr "L'acheteur n'est pas un commerçant"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__calculate_parcel_method
msgid "Calculate Parcel Method"
msgstr "Méthode de calcul de colis"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_shipping_info_wizard_form
msgid "Cancel"
msgstr "Annuler"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Transporteur"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__carrier_code
msgid "Carrier Code"
msgstr "Code du transporteur"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Click on the link to know how to get your Ankorstore Client ID and Client "
"secret in Ankorstore"
msgstr ""
"Cliquez sur le lien pour savoir comment obtenir votre ID client et votre "
"secret client Ankorstore dans Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__client_id
msgid "Client ID"
msgstr "ID client"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__client_secret
msgid "Client Secret"
msgstr "Secret client"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Client secret"
msgstr "Secret Client"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__collection_method
msgid "Collection Method"
msgstr "Méthode de collecte"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__company_id
msgid "Company"
msgstr "Société"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Confirm selected quote ?"
msgstr "Confirmer le devis sélectionné ?"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Confirm whether you are part of the AnkorLogistics Fulfilment Center "
"programme"
msgstr ""
"Confirmez si vous faites partie du programme AnkorLogistics Fulfilment "
"centre"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__connected
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Connected"
msgstr "Connecté"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_res_partner
msgid "Contact"
msgstr ""

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Content hazardous goods"
msgstr "Contenu marchandises dangereuses"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_aks_test_create_order
msgid "Create"
msgstr "Créer"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Create Internal Order as a retailer"
msgstr "Créer une commande interne en tant que commerçant"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_test_order_wizard
msgid "Create Test Order"
msgstr "Créer une commande de test"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_test_order_line_wizard
msgid "Create Test Order Line"
msgstr "Créer une ligne de commande de test"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__create_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__create_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__create_date
msgid "Created on"
msgstr "Créé le"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Credentials"
msgstr "Informations d'identification"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__currency
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__currency
msgid "Currency"
msgstr "Devise"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__currencyRate
msgid "Currency Rate"
msgstr "Taux de change"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__customer_order_ref
msgid "Customer Order Reference"
msgstr "Référence de commande client"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_order_form_aks
msgid "Customer Reference"
msgstr "Référence client"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Deactivate connection"
msgstr "Désactiver la connexion"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_default_analytic_account
msgid "Default Analytic Account"
msgstr "Compte analytique par défaut"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__followers_ids
msgid "Default Followers"
msgstr "Suiveurs par défaut"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_default_invoice_journal
msgid "Default Invoice Journal"
msgstr "Journal de facturation par défaut"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__package_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_delivery_carrier__aks_default_package_type_id
msgid "Default Package Type"
msgstr "Type de colis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_default_warehouse_id
msgid "Default Warehouse when create order from AKS"
msgstr "Entrepôt par défaut quand création commande depuis AKS"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Default warehouse"
msgstr "Entrepôt par défaut"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Define a dedicated Sales team assigned to all ANKORSTORE orders - useful for"
" reporting"
msgstr ""
"Définir une équipe commerciale dédiée affectée à toutes les commandes "
"ANKORSTORE - utile pour les rapports"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Define a name to recognize this Ankorstore connection"
msgstr "Donnez un nom pour reconnaître cette connexion Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Define the default warehouse to the AKS orders import"
msgstr "Définissez l'entrepôt par défaut pour l'import des commandes AKS"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Define the way you want to identify Ankorstore commissions in your Odoo "
"orders. We recommend using a dedicated service product"
msgstr ""
"Définissez la manière dont vous souhaitez identifier les commissions "
"Ankorstore dans vos commandes Odoo. Nous vous recommandons d'utiliser un "
"produit de service dédié"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Define whether the invoicing partner should be the end customer (unticked), "
"or Ankorstore (ticked)"
msgstr ""
"Définir si le partenaire de facturation doit être le client final (non "
"coché), ou Ankorstore (coché)"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Define which people in your organization should receive potential backend "
"errors"
msgstr ""
"Définir les personnes de votre organisation qui devraient recevoir les "
"erreurs potentielles du backend"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Define/Select the Ankorstore FC in Odoo"
msgstr "Créer/Sélectionner le FC Ankorstore dans Odoo"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Define/Select the Ankorstore FC internal location code in Odoo"
msgstr ""
"Créer/Sélectionner le code d'emplacement interne du FC Ankorstore dans Odoo"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Define/Select the Ankorstore FC shipping method in Odoo"
msgstr "Créer/Sélectionner la méthode d'expédition du FC Ankorstore dans Odoo"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Assistant de sélection du transporteur"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__delivery_type
msgid "Delivery Type"
msgstr "Fournisseur"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks__environment__dev
msgid "Development"
msgstr "Développement"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_aks_reject_wizard
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_aks_test_create_order
msgid "Discard"
msgstr "Annuler"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__display_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__display_name
msgid "Display Name"
msgstr "Nom à afficher"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_queue_job__state__done
msgid "Done"
msgstr "Terminé"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_integration_status
msgid "Enable or Disable the integration with Ankorstore."
msgstr "Activer ou Désactiver l'intégration avec Ankorstore."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_shipping_info_wizard__tracking_url
msgid "Enter the tracking URL of the shipment."
msgstr "Entrez le lien de suivi de l'expédition."

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_shipping_info_wizard_form
msgid "Enter tracking URL if available"
msgstr "Entrez le lien de suivi si disponible"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Entry package"
msgstr "Forfait d'entrée"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__environment
msgid "Environment"
msgstr "Environnement"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__error_message
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__error_message
msgid "Error Message"
msgstr "Message d'erreur"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Estimate Package"
msgstr "Forfait devis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__event_type
msgid "Event Type"
msgstr "Type d'événement"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Export inventory to Ankorstore"
msgstr "Exporter l'inventaire vers Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__external_order_arrived
msgid "External Order Arrived"
msgstr "Commande externe arrivée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__external_order_awaiting_fulfillment
msgid "External Order Awaiting Fulfillment"
msgstr "Commande externe en attente de traitement"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__external_order_cancelled
msgid "External Order Cancelled"
msgstr "Commande externe annulée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__external_order_created
msgid "External Order Created"
msgstr "Commande externe créée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__external_order_shipped
msgid "External Order Shipped"
msgstr "Commande externe expédiée"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_fc_location_id
msgid "FC Internal Location"
msgstr "Emplacement interne du FC"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "FC Odoo warehouse"
msgstr "Entrepôt Odoo du FC"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "FC internal location"
msgstr "Emplacement interne du FC"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "FC shipping method"
msgstr "Méthode d'expédition du FC"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_queue_job__state__failed
msgid "Failed"
msgstr "Échoué"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_fees_product
msgid "Fees service product"
msgstr "Produit de service de frais"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Find Ankorstore among the invoicing partners you have defined in Odoo; Or "
"create it if it is not setup yet"
msgstr ""
"Trouver Ankorstore parmi les partenaires de facturation que vous avez "
"définis dans Odoo ; ou le créer s'il n'est pas encore configuré"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_follower_ids
msgid "Followers"
msgstr "Suiveurs"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_partner_ids
msgid "Followers (Partners)"
msgstr "Suiveurs (Partenaires)"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__model
msgid "From Model"
msgstr "Depuis le modèle"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_move__fulfillableid
msgid "Fulfillable ID"
msgstr "ID exécutable"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_fc_wh_id
msgid "Fulfillment Center Warehouse (FC)"
msgstr "Entrepôt du fullfilment centre des commandes (FC)"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_mode
msgid "Fulfillment Mode"
msgstr "Mode de traitement des commandes"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_mode__ffa
msgid "Fulfillment by Ankorstore"
msgstr "Traitement des commandes par Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__fulfillment_requested
msgid "Fulfillment requested"
msgstr "Traitement des commandes demandé"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__fulfillment_requested
msgid "Fulfillment requested by the brand"
msgstr "Traitement des commandes demandé par la marque"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order_line__fulfillableid
msgid "Fulfilment ID"
msgstr "ID de traitement des commandes"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_sale_order_line__fulfillableid
#: model:ir.model.fields,help:connector_ankorstore.field_stock_move__fulfillableid
msgid "Fulfilment ID from Ankorstore"
msgstr "ID de traitement des commandes depuis Ankorstore"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Get Quotes"
msgstr "Obtenir des devis"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Get your unique Client ID from your Ankorstore account to authenticate the "
"connection between Ankorstore and Odoo"
msgstr ""
"Obtenez votre ID client unique de votre compte Ankorstore pour authentifier "
"la connexion entre Ankorstore et Odoo"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Get your unique Client secret from your Ankorstore account to authenticate "
"the connection between Ankorstore and Odoo"
msgstr ""
"Obtenez votre secret client unique de votre compte Ankorstore pour "
"authentifier la connexion entre Ankorstore et Odoo"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__has_message
msgid "Has Message"
msgstr "A un message"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__is_hazardous_goods
msgid "Hazardous Goods?"
msgstr "Marchandises dangereuses ?"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Help"
msgstr "Aide"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__hide_aks_picking
msgid "Hide standard actions"
msgstr "Masquer les actions standard"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__id
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__id
msgid "ID"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, les nouveaux messages nécessitent votre attention."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__message_has_error
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__active
msgid ""
"If the active field is set to False, it will allow you to hide the backend "
"without removing it."
msgstr ""
"Si le champ actif est défini sur Faux, cela vous permettra de masquer le "
"backend sans le supprimer."

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.res_config_settings_view_form_connector_ankorstore
msgid "Import Ankorstore orders and manage fulfillment"
msgstr ""
"Importer les commandes Ankorstore et gérer le traitement des commandes"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__connected
msgid "Indicates if the backend is connected to Ankorstore."
msgstr "Indiques si le backend est connecté à Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_integration_status
msgid "Integration Status"
msgstr "Status d'intégration"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_mode__ffb
msgid "Internal Fulfillment"
msgstr "Traitement interne des commandes"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_stock_location
msgid "Inventory Locations"
msgstr "Emplacements de l'inventaire"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Invoicing"
msgstr "Facturation"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Invoicing partner"
msgstr "Partenaire de facturation"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_warehouse__is_aks_fc
msgid "Is AKS Fulfillment Center"
msgstr "Est un fulfillment centre AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_location__is_aks_fc
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_move__is_aks_fc
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__is_aks_fc
msgid "Is AKS Location Fulfillment Center"
msgstr "Est un fulfillment centre AKS"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Is AnkorLogistics Fulfilment Center ?"
msgstr "Est un fulfilment centre AnkorLogistics ?"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_is_follower
msgid "Is Follower"
msgstr "Est un Follower"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_active_fc
msgid "Is Fulfillment Center Active ?"
msgstr "Le fulfillment centre est-il actif ?"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_product__is_aks_exported
#: model:ir.model.fields,field_description:connector_ankorstore.field_product_template__is_aks_exported
msgid "Is the product exported to AKS"
msgstr "Le produit est-il exporté vers AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__kwargs
msgid "Kwargs"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__last_aks_status
msgid "Last AKS status"
msgstr "Dernier statut AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__write_uid
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__write_date
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_last_synchronization
msgid "Last synchronization date"
msgstr "Date de la dernière synchronisation"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Learn more"
msgstr "Plus d'informations"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__log_folllower_ids
msgid "Log Followers"
msgstr "Suiveurs du journal"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Log followers"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__log_folllower_ids
msgid "Log followers for the errors in the backend."
msgstr "Suiveurs du journal pour les erreurs dans le backend."

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.res_config_settings_view_form_connector_ankorstore
msgid "Manage API Credentials"
msgstr "Gérer les informations d'identification de l'API"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__choose_delivery_carrier__calculate_parcel_method__manual
msgid "Manual"
msgstr "Manuel"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_fc_shipping_method_id
msgid "Mapping FC Shipping Method to retrieve tracking"
msgstr "Mapper la méthode d'expédition du FC pour récupérer le suivi"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Marketplace Stock"
msgstr "Stock du marché"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_has_error
msgid "Message Delivery error"
msgstr "Erreur de livraison du message"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_ids
msgid "Messages"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__method_name
msgid "Method Name"
msgstr "Nom de la méthode"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Ma date limite d'activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__name
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__name
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__name
msgid "Name"
msgstr "Nom"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__ankor_confirmed
msgid "New Ankorstore order"
msgstr "Nouvelle commande Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prochaine date limite d'activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de la prochaine activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_type_id
msgid "Next Activity Type"
msgstr "Type de la prochaine activité"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec une erreur de livraison"

#. module: connector_ankorstore
#: model:ir.model.constraint,message:connector_ankorstore.constraint_backend_aks_unique_company_aks_backend
msgid "Only one AKS backend per company is allowed."
msgstr "Seulement un backend AKS par société est autorisé."

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__operation
msgid "Operation"
msgstr "Opération"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__order_id
msgid "Order"
msgstr "Commande"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_cancelled
msgid "Order Cancelled"
msgstr "Commande annulée"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order_line__orderitemid
msgid "Order Item ID"
msgstr "ID de l'article de la commande"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_sale_order_line__orderitemid
msgid "Order Item ID from Ankorstore"
msgstr "ID de l'article de la commande depuis Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__line_ids
msgid "Order Lines"
msgstr "Lignes de commande"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipped
msgid "Order Shipped"
msgstr "Commande expédiée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__received
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__arrived
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__received
msgid "Order is arrived and completed"
msgstr "Commande est arrivée et complétée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__cancelled
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__cancelled
msgid "Order is cancelled"
msgstr "Commande annulée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__invoiced
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__invoiced
msgid "Order is invoiced"
msgstr "Commande est facturée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__rejected
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__rejected
msgid "Order is rejected by the brand"
msgstr "Commande est rejetée par la marque"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__shipped
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__shipped
msgid "Order is shipped from the warehouse"
msgstr "Commande est expédiée depuis l'entrepôt"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__awaiting_fulfillment
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__awaiting_fulfillment
msgid "Order is waiting for fulfillment"
msgstr "Commande est en attente de traitement"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__order_items_prices_incorrect
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__order_items_prices_incorrect
msgid "Order items prices incorrect"
msgstr "Prix incorrects des articles de la commande"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_auto_skip_reject
msgid "Orders are already confirmed when they are imported from AKS"
msgstr ""
"Les commandes sont déjà confirmées lorsqu'elles sont importées depuis AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_auto_confirm_orders
msgid "Orders are automaticly assigned to ANKORSTORE Fulfillment Center"
msgstr ""
"Les commandes sont automatiquement attribuées au fulfillment centre "
"ANKORSTORE"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__other
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__other
msgid "Other"
msgstr "Autre"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__delivery_carrier__aks_label_format__pdf
msgid "PDF"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_package_line
#: model:ir.model,name:connector_ankorstore.model_shipping_package_line
msgid "Package Line"
msgstr "Ligne de colis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__package_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__package_id
msgid "Package Type"
msgstr "Type de colis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__package_line_ids
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__package_line_ids
msgid "Packages"
msgstr "Colis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__parcel_number
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__parcel_number
msgid "Parcel Number"
msgstr "Numéro de colis"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__payment_issues_with_retailer
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__payment_issues_with_retailer
msgid "Payment issues with retailer"
msgstr "Problèmes de paiement avec le commerçant"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_queue_job__state__pending
msgid "Pending"
msgstr "En attente"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Pre-filled field with your Odoo information"
msgstr "Champ pré-rempli avec vos informations Odoo"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__preparation_time_too_high
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__preparation_time_too_high
msgid "Preparation time too high"
msgstr "Temps de préparation trop long"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_stock_picking_form_inherit
msgid "Print Labels"
msgstr "Imprimer étiquettes"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_queue_job__state__processing
msgid "Processing"
msgstr "En cours de traitement"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_product_template
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__product_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__product_id
msgid "Product"
msgstr "Produit"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__product_out_of_stock
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__product_out_of_stock
msgid "Product out of stock"
msgstr "Produit en rupture de stock"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks__environment__prod
msgid "Production"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Fournisseur"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__purchase_not_for_resale
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__purchase_not_for_resale
msgid "Purchase not for resale"
msgstr "Achat non destiné à la revente"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__quantity
msgid "Quantity"
msgstr "Quantité"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_queue_job
msgid "Queue Job"
msgstr "Tâche de file d'attente"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_quote_lookup
msgid "Quote Selection"
msgstr "Sélection de devis"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__quote_uuid
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__quote_uuid
msgid "Quote UUID"
msgstr "UUID du devis"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Quotes Results"
msgstr "Résultats des devis"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__reception_refused
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__reception_refused
msgid "Reception refused by the retailer"
msgstr "Réception refusée par le commerçant"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Reconcile catalog"
msgstr "Réconcilier le catalogue"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_aks_reject_wizard
msgid "Reject"
msgstr "Rejeter"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_order_form_aks
msgid "Reject AKS Order"
msgstr "Rejeter la commande AKS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__aks_reject_details
#: model:ir.model.fields,field_description:connector_ankorstore.field_sale_order__aks_reject_details
msgid "Reject Details"
msgstr "Détails de rejet"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_aks_reject_wizard
msgid "Reject with reaseon Selection"
msgstr "Rejeter avec sélection de raison"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__response_data
msgid "Response Data"
msgstr "Données de réponse"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__line_ids
msgid "Results"
msgstr "Résultats"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_wizard__retailer
msgid "Retailer ID"
msgstr "ID du commerçant"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__retailer_vat_number_missing
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__retailer_vat_number_missing
msgid "Retailer VAT number missing"
msgstr "Numéro de TVA du commerçant manquant"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__retailer_agreed_to_do_changes_to_order
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__retailer_agreed_to_do_changes_to_order
msgid "Retailer agreed to do changes to order"
msgstr "Le commerçant a accepté de modifier la commande"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__aks_reject_wizard__aks_reject_reason__retailer_not_good_fit_for_brand
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__aks_reject_reason__retailer_not_good_fit_for_brand
msgid "Retailer not good fit for brand"
msgstr "Le commerçant n'est pas adapté à la marque"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Retrieve FC inventory from Ankorstore"
msgstr "Récupérer l'inventaire du FC depuis Ankorstore"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__retry
msgid "Retry"
msgstr "Réessayer"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur de livraison SMS"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_reject_wizard__sale_order_id
msgid "Sale Order"
msgstr "Commande de vente"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Sales"
msgstr "Ventes"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_sale_order
msgid "Sales Order"
msgstr "Bon de commande"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__sale_order_default_team
msgid "Sales Team"
msgstr "Équipe de vente"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Sales and Operations"
msgstr "Ventes et Opérations"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Sales team"
msgstr "Équipe de vente"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Salesperson"
msgstr "Vendeur"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.choose_delivery_carrier_view_form_inherit
msgid "Select"
msgstr "Sélectionner"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid ""
"Select which stock location(s) you want to use to feed stock availability on"
" Ankorstore. You may select one or many different locations. If you use "
"Ankorstore Fulfilment Center, we recommend that you add it"
msgstr ""
"Sélectionnez les emplacements de stock que vous souhaitez utiliser pour "
"alimenter la disponibilité du stock sur Ankorstore. Vous pouvez sélectionner"
" un ou plusieurs emplacements différents. Si vous utilisez le fulfilment "
"centre Ankorstore, nous vous recommandons de l'ajouter"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__service_code
msgid "Service Code"
msgstr "Code de service"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__service_commercial_name
#: model:ir.model.fields,field_description:connector_ankorstore.field_choose_delivery_carrier__service_commercial_name
msgid "Service Commercial Name"
msgstr "Nom commercial du service"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__is_aks_invoicing_partner
msgid "Set Ankorstore partner as default invoicing partner"
msgstr ""
"Définir le partenaire Ankorstore comme partenaire de facturation par défaut"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Setup"
msgstr "Configuration"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipment_received
msgid "Shipment Received"
msgstr "Expédition reçue"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipment_refused
msgid "Shipment Refused"
msgstr "Expédition refusée"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipping_address_updated
msgid "Shipping Address Updated"
msgstr "Adresse de livraison mise à jour"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_shipping_info_wizard_form
msgid "Shipping Information"
msgstr "Informations de livraison"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipping_labels_generated
msgid "Shipping Labels Generated"
msgstr "Étiquettes d'expédition générées"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__backend_aks_webhook__event_type__order_shipping_labels_generated_reverted
msgid "Shipping Labels Generated Reverted"
msgstr "Étiquettes d'expédition générées annulées"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Modes d'expédition"

#. module: connector_ankorstore
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__sale_order__last_aks_status__shipping_labels_generated
#: model:ir.model.fields.selection,name:connector_ankorstore.selection__stock_picking__aks_sync_status__shipping_labels_generated
msgid "Shipping labels generated"
msgstr "Étiquettes d'expédition générées"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_stock_picking__show_aks_confirm_custom_ship
msgid "Show AKS Confirm Custom Ship"
msgstr "Afficher AKS Confirmer l'expédition personnalisée"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_queue_job__state
msgid "State"
msgstr "État"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"Dépassé: La date d'échéance est déjà passée\n"
"Aujourd'hui: La date de l'activité est aujourd'hui\n"
"Prévu: Activités futures."

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_stock_move
msgid "Stock Move"
msgstr "Mouvement de stock"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Stock location(s) to sync to Ankorstore"
msgstr "Emplacement(s) de stock à synchroniser avec Ankorstore"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_stock_package_type
msgid "Stock package type"
msgstr "Type de colis de stock"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_shipping_info_wizard_form
msgid "Submit"
msgstr "Soumettre"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__subscription_active
msgid "Subscription Active"
msgstr "Abonnement actif"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__subscription_id
msgid "Subscription ID"
msgstr "ID d'abonnement"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_stock_picking__aks_sync_status
msgid "THe synchronization status of the delivery order to ANKORSTORE:\n"
msgstr ""
"Le statut de synchronisation de la commande de livraison vers ANKORSTORE:\n"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__sale_order_default_team
msgid "The Sales Team assigned to ANKORSTORE orders for reporting"
msgstr ""
"L'équipe de vente attribuée aux commandes ANKORSTORE pour le reporting"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_stock_picking__aks_id
msgid "The internal order ID in the Ankorstore system."
msgstr "L'ID de commande interne dans le système Ankorstore."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_app_name
msgid "The name of the application to subscribe to webhooks."
msgstr "Le nom de l'application pour s'abonner aux webhooks."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__aks_partner
msgid "The partner that represents Ankorstore as an accounting partner."
msgstr ""
"Le partenaire qui représente Ankorstore en tant que partenaire comptable."

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__name
msgid "The user-defined name of the ANKORSTORE account"
msgstr "Le nom défini par l'utilisateur du compte ANKORSTORE"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__timestamp
msgid "Timestamp"
msgstr "Horodatage"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_info_wizard__tracking_url
msgid "Tracking URL"
msgstr "Lien de suivi"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_stock_picking
msgid "Transfer"
msgstr "Transfert"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__type
msgid "Type"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type de l'activité d'exception sur l'enregistrement."

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Unconnected"
msgstr "Déconnecté"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_webhook_form
msgid "Unsubscribe"
msgstr "Se désabonner"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__use_webhooks
msgid "Use Webhooks"
msgstr "Utiliser les webhooks"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__client_id
#: model:ir.model.fields,help:connector_ankorstore.field_backend_aks__client_secret
msgid "Used in AKS Connect authorization code flow for confidential clients."
msgstr ""
"Utilisé dans le flux de code d'autorisation AKS Connect pour les clients "
"confidentiels."

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_fees_line__vatRate
msgid "VAT Rate"
msgstr "Taux de TVA"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_stock_warehouse
msgid "Warehouse"
msgstr "Entrepôt"

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_webhook_form
msgid "Webhook"
msgstr ""

#. module: connector_ankorstore
#: model_terms:ir.ui.view,arch_db:connector_ankorstore.view_backend_aks_form
msgid "Webhook Base URL"
msgstr "URL par défaut du webhook"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks_webhook__url
msgid "Webhook URL"
msgstr "URL du webhook"

#. module: connector_ankorstore
#: model:ir.ui.menu,name:connector_ankorstore.webhook_subscriptions
msgid "Webhook subscriptions"
msgstr "Abonnements aux webhooks"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_backend_aks__aks_webhook_ids
msgid "Webhooks"
msgstr ""

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_sync_error__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: connector_ankorstore
#: model:ir.model.fields,help:connector_ankorstore.field_aks_sync_error__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__parcel_weight
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__parcel_weight
msgid "Weight by parcel(Kg)"
msgstr "Poid par colis(Kg)"

#. module: connector_ankorstore
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_package_line__wizard_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_quote_lookup__wizard_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_aks_test_order_line_wizard__wizard_id
#: model:ir.model.fields,field_description:connector_ankorstore.field_shipping_package_line__wizard_id
msgid "Wizard"
msgstr "Assistant"

#. module: connector_ankorstore
#: model:ir.model,name:connector_ankorstore.model_shipping_info_wizard
msgid "Wizard for Entering Shipping Information"
msgstr "Assistant pour saisir les informations de livraison"
