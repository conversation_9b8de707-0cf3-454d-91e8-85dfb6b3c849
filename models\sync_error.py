# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import psycopg2
from markupsafe import Markup
from odoo import SUPERUSER_ID, api, fields, models, registry


class AksProductSyncError(models.Model):
    """Dedicated model to log synchronization AKS errors"""

    _inherit = ["mail.thread", "mail.activity.mixin"]
    _name = "aks.sync.error"
    _description = "AKS Synchronization Errors"

    model = fields.Char(string="From Model")
    operation = fields.Char(string="Operation")
    name = fields.Char(string="Name")
    error_message = fields.Text(string="Error Message")
    timestamp = fields.Datetime(string="Timestamp", default=lambda self: fields.Datetime.now())
    followers_ids = fields.Many2many("res.partner", string="Default Followers")  # , default=lambda self: self.env["backend.aks"].get_followers())

    def _logme(self, **kwargs):
        """
        sample logme(**kwargs)
        need to be on new_env to avoid rollback from the main transaction
        """
        self.env.flush_all()
        db_name = self._cr.dbname
        try:
            db_registry = registry(db_name)
            with db_registry.cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                IrLogging = env["aks.sync.error"]
                IrLogging.sudo().create(kwargs)
        except psycopg2.Error:
            pass

    @api.model_create_multi
    def create(self, vals):
        res = super().create(vals)
        res.message_subscribe(partner_ids=res.followers_ids.ids)
        body = Markup("Error: %s") % res.error_message

        res.message_post(
            body=body,
            subtype_xmlid="mail.mt_comment",
            message_type="comment",
        )
        return res
