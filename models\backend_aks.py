# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
import logging

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError

from ..aks.aks import AksClient as AKS

_logger = logging.getLogger(__name__)

WEBHOOK_EVENTS = [
    ("order.brand_created", "Brand Order Created"),
    ("order.brand_accepted", "Brand Order Accepted"),
    ("order.brand_rejected", "Brand Order Rejected"),
    ("order.billing_address_updated", "Billing Address Updated"),
    ("order.shipping_address_updated", "Shipping Address Updated"),
    ("order.shipping_labels_generated", "Shipping Labels Generated"),
    ("order.shipped", "Order Shipped"),
    ("order.shipment_received", "Shipment Received"),
    ("order.shipment_refused", "Shipment Refused"),
    ("order.brand_paid", "Brand Paid"),
    ("order.cancelled", "Order Cancelled"),
    ("order.brand_accepted_reverted", "Brand Accepted Reverted"),
    ("order.shipping_labels_generated_reverted", "Shipping Labels Generated Reverted"),
    ("external_order.created", "External Order Created"),
    ("external_order.awaiting_fulfillment", "External Order Awaiting Fulfillment"),
    ("external_order.cancelled", "External Order Cancelled"),
    ("external_order.shipped", "External Order Shipped"),
    ("external_order.arrived", "External Order Arrived"),
]


class BackendAks(models.Model):
    """
    Ankorstore Backend Model.

    This model assures the backend configuration for Ankorstore.
    Assume one backend per company for  one environment (prod or dev).
    """

    _name = "backend.aks"
    _description = "Ankorstore Backend"
    _check_company_auto = True

    name = fields.Char(
        string="Name",
        help="The user-defined name of the ANKORSTORE account",
        compute="_compute_name",
    )
    active = fields.Boolean(
        default=True,
        required=True,
        help="If the active field is set to False, it will allow you to hide the backend without removing it.",
    )
    connected = fields.Boolean(
        string="Connected",
        default=False,
        help="Indicates if the backend is connected to Ankorstore.",
    )
    company_id = fields.Many2one(
        string="Company",
        comodel_name="res.company",
        default=lambda self: self.env.company,
        required=True,
        readonly=True,
    )

    # Credentials fields
    base_url_prod = fields.Char(
        string="Base URL PRODUCTION",
        required=True,
        default="https://www.ankorstore.com",
    )
    base_url_dev = fields.Char(
        string="Base URL STAGING",
        required=True,
        default="https://www.public.ankorstore-sandbox.com",
    )
    client_id = fields.Char(
        string="Client ID",
        help="Used in AKS Connect authorization code flow for confidential clients.",
        required=True,
    )
    client_secret = fields.Char(
        string="Client Secret",
        help="Used in AKS Connect authorization code flow for confidential clients.",
        required=True,
    )
    environment = fields.Selection(
        [
            ("prod", "Production"),
            ("dev", "Development"),
        ],
        string="Environment",
        required=True,
        default="prod",
    )

    log_folllower_ids = fields.Many2many(
        "res.partner",
        "id",
        string="Log Followers",
        help="Log followers for the errors in the backend.",
        default=lambda self: self.res_users_id.partner_id.ids,
    )

    # WEBHOOKS
    use_webhooks = fields.Boolean(string="Use Webhooks", default=True)
    aks_app_name = fields.Char(
        "Application Name",
        default="Odoo",
        help="The name of the application to subscribe to webhooks.",
    )
    aks_app_id = fields.Char("Application ID")

    odoo_webhook_base_url = fields.Char(string="Base Odoo URL for Webhooks", compute="_compute_odoo_webhook_base_url")

    aks_webhook_ids = fields.One2many("backend.aks.webhook", "backend_id", string="Webhooks")

    # ANKORSTORE SETTINGS - SALES / INVENTORY / DELIVERY
    res_users_id = fields.Many2one(
        string="Assigned Salesperson",
        comodel_name="res.users",
        default=lambda self: self.env.user,
        check_company=True,
    )
    sale_order_default_team = fields.Many2one(
        string="Sales Team",
        help="The Sales Team assigned to ANKORSTORE orders for reporting",
        comodel_name="crm.team",
        check_company=True,
    )
    aks_partner = fields.Many2one(
        "res.partner",
        string="Ankorstore Billing Partner",
        help="The partner that represents Ankorstore as an accounting partner.",
        # required=True,
        check_company=True,
        domain="[('is_company', '=', True)]",
    )
    is_aks_invoicing_partner = fields.Boolean("Set Ankorstore partner as default invoicing partner", default=False)
    aks_fees_product = fields.Many2one(
        "product.product",
        string="Fees service product",
        domain="[('type', '=', 'service')]",
        check_company=True,
        required=True,
    )
    aks_default_warehouse_id = fields.Many2one("stock.warehouse", string="Default Warehouse when create order from AKS", check_company=True, required=True)
    aks_sync_allowed_location_ids = fields.Many2many(
        "stock.location",
        string="Allowed Stock Locations to sync in AKS",
        check_company=True,
    )
    aks_active_fc = fields.Boolean("Is Fulfillment Center Active ?", default=False)

    aks_auto_confirm_orders = fields.Boolean(
        "Automatically route by Ankorstore FC",
        default=False,
        help="Orders are automaticly assigned to ANKORSTORE Fulfillment Center",
    )
    aks_auto_skip_reject = fields.Boolean(
        "Auto confirm Ankorstore Orders",
        default=False,
        help="Orders are already confirmed when they are imported from AKS",
    )
    aks_fc_wh_id = fields.Many2one(
        "stock.warehouse",
        string="Fulfillment Center Warehouse (FC)",
        check_company=True,
        domain="[('is_aks_fc', '=', True)]",
    )
    aks_fc_location_id = fields.Many2one(
        "stock.location",
        string="FC Internal Location",
        check_company=True,
        domain="[('is_aks_fc', '=', True), ('usage', '=', 'internal')]",
    )

    aks_fc_shipping_method_id = fields.Many2one(
        "delivery.carrier",
        string="Mapping FC Shipping Method to retrieve tracking",
        check_company=True,
        domain="[('delivery_type', '=', 'aks')]",
    )

    aks_default_invoice_journal = fields.Many2one("account.journal", string="Default Invoice Journal", check_company=True)

    aks_default_analytic_account = fields.Many2one(
        "account.analytic.account",
        string="Default Analytic Account",
        check_company=True,
    )

    aks_integration_status = fields.Boolean(
        string="Integration Status",
        default=False,
        help="Enable or Disable the integration with Ankorstore.",
    )
    aks_auto_confirm_quote = fields.Boolean(
        string="Auto Confirm Quote",
        default=False,
        help="Automatically confirm quotes in Ankorstore.",
    )
    # For Tests only
    response_data = fields.Text(string="Response Data", readonly=True)
    # CRUD METHODS

    @api.onchange("aks_integration_status")
    def _onchange_aks_integration_status(self):
        if self.id:
            if self.action_integration_status():
                return {
                    "warning": {
                        "title": _("Warning"),
                        "message": _("Ankorstore Integration Status Updated."),
                    }
                }

    @api.onchange("environment")
    def _onchange_environment(self):
        return {
            "warning": {
                "title": _("Warning"),
                "message": _("Changing the environment will affect the base URL. Please update your credentials accordingly."),
            }
        }

    @api.onchange("aks_auto_confirm_orders")
    def _onchange_aks_auto_confirm_orders(self):
        return {
            "warning": {
                "title": _("Warning"),
                "message": _("Check “Autoassign Ankorstore orders to FC” only if you are sure that your setting in Ankorstore is similar."),
            }
        }

    @api.onchange("aks_auto_skip_reject")
    def _onchange_aks_auto_skip_reject(self):
        return {
            "warning": {
                "title": _("Warning"),
                "message": _("Check “Autoconfirm Ankorstore orders” only if you are sure that your setting in Ankorstore is similar."),
            }
        }

    @api.depends("company_id", "environment")
    def _compute_name(self):
        for record in self:
            record.name = f"{record.company_id.name} - {record.environment}"

    def _compute_odoo_webhook_base_url(self):
        """Compute the base URL for webhooks from system parameters."""
        for record in self:
            base_url = self.env["ir.config_parameter"].sudo().get_param("web.base.url")
            record.odoo_webhook_base_url = f"{base_url}/connector_aks/webhooks/{record.company_id.id}/"

    def action_integration_status(self):
        # https://www.public.ankorstore-sandbox.com/api/v1/integration/external-integrations/-actions/update-status
        aks_integration_status = "enabled" if self.aks_integration_status else "disabled"
        payload = {
            "platform": "odoo",
            "status": aks_integration_status,
        }
        try:
            client = self.get_instance()
            response = client.make_api_request(
                method="POST",
                path="api/v1/integration/external-integrations/-actions/update-status",
                data=payload,
            )
            # Update the 'response_data' field with the response

            if response.status_code == 200:
                return True
        except Exception as e:
            error_message = f"Error while calling AKS API : {e}"
            self.env["aks.sync.error"]._logme(
                model="backend.aks",
                operation="action_integration_status",
                name=self.name,
                error_message=error_message,
            )
            raise ValidationError(e)

    def action_integration_activate(self):
        self.aks_integration_status = True
        return self.action_integration_status()

    def action_integration_deactivate(self):
        self.aks_integration_status = False
        return self.action_integration_status()

    # constraints
    # add a constaint unique company_id and environment
    _sql_constraints = [
        (
            "unique_company_aks_backend",
            "unique(company_id)",
            "Only one AKS backend per company is allowed.",
        )
    ]

    # default get
    @api.model
    def default_get(self, fields_list):
        defaults = super().default_get(fields_list)
        defaults["aks_fc_wh_id"] = self._get_default_aks_fc_wh_id().id
        defaults["aks_fc_location_id"] = self._get_default_aks_fc_location_id().id
        return defaults

    def _get_company(self):
        company_id = False
        if self.env.context.get("default_company_id"):
            company_id = self.env.context["default_company_id"]
        if not company_id and self.env.company:
            company_id = self.env.company.id
        return company_id

    def get_params(self):
        company_id = self._get_company()
        ret = self.search([("company_id", "=", company_id)], limit=1)
        return ret

    def _get_default_aks_fc_wh_id(self):
        return self.env["stock.warehouse"].search([("is_aks_fc", "=", True)], limit=1)

    def _get_default_aks_fc_location_id(self):
        return self.env["stock.location"].search([("is_aks_fc", "=", True), ("usage", "=", "internal")], limit=1)

    def get_followers(self):
        company = self._get_company()
        res = self.search([("company_id", "=", company)], limit=1)
        if not res:
            raise ValidationError(_("No AKS backend found for company %s") % self.env["res.company"].browse(company).name)
        return res.log_folllower_ids.ids

    def get_base_url(self):
        """
        Get the base URL for the current environment.
        """
        company = self._get_company()
        res = self.search([("company_id", "=", company)], limit=1)
        if not res:
            raise ValidationError(_("No AKS backend found for company %s") % self.env["res.company"].browse(company).name)
        return res.base_url_dev if not res._is_prod() else res.base_url_prod

    def get_instance(self):
        """
        Returns an instance of the AKS client from self.env.company.
        """
        company = self._get_company()
        res = self.search([("company_id", "=", company)], limit=1)
        if not res:
            self.env["aks.sync.error"]._logme(
                model="backend.aks",
                operation="get_instance",
                name=self.env.company.name,
                error_message=f"No AKS backend found for company {self.env.company.name}",
            )
            raise ValidationError(_("No AKS backend found for company %s") % self.env["res.company"].browse(company).name)

        try:
            base_url = res.base_url_dev if not res._is_prod() else res.base_url_prod
            client = AKS(base_url, res.client_id, res.client_secret)
            return client
        except Exception as e:
            error_message = f"Error while creating AKS client for backend {res.name} : {e}"
            self.env["aks.sync.error"]._logme(
                model="backend.aks",
                operation="get_instance",
                name=res.name,
                error_message=error_message,
            )
            _logger.warning(error_message)

    # AKS API METHODs - ORM WRAPPER
    def get_session(self):
        """
        Get a json:api session.
        """
        client = self.get_instance()
        return client

    @api.model
    def _is_prod(self):
        # test ir.config_parameter is database is neutralized then force dev env
        staging = self.env["ir.config_parameter"].sudo().get_param("database.is_neutralized")
        if staging or self.environment == "dev":
            return False
        else:
            return True

    @api.model
    def inclusion(self, *args):
        """
        Get a json:api inclusion.
        """
        return AKS.inclusion(*args)

    @api.model
    def modifier(self, *args):
        """
        Get a json:api modifier.
        """
        return AKS.modifier(*args)

    @api.model
    def filter(self, *args):
        """
        Get a json:api filter.
        """
        return AKS.filter(*args)

    def action_whoiam(self):
        """
        Make a 'Who Am I' API call.
        A basic API call that does not use json_api.
        It fetches the details of a specific order and updates
        the 'response_data' field with the response.
        """
        try:
            client = self.get_instance()
            response = client.make_api_request(method="GET", path="api/v1/me")
            # (
            # scheme='https',
            # netloc='www.public.ankorstore-sandbox.com',
            # path='/api/v1/orders/1ee221d7-b717-6c94-bdb7-9e21e4fe0ba4',
            # params='', query='include=billingItems,orderItems.productVariant.product',
            # fragment=''
            # )
            # Update the 'response_data' field with the response
            self.write({"response_data": response.json()})
            return {
                "name": "Who Am I",
                "type": "ir.actions.act_window",
                "res_model": "backend.aks",
                "res_id": self.id,
                "view_mode": "form",
                "view_id": self.env.ref("connector_ankorstore.view_backend_aks_whoiam").id,
                "target": "new",
                "context": self.env.context,
            }
        except Exception as e:
            error_message = f"Error while calling AKS API : {e}"
            self.env["aks.sync.error"]._logme(
                model="backend.aks",
                operation="action_whoiam",
                name=self.name,
                error_message=error_message,
            )
            raise ValidationError(e)

    def action_list_catalog(self):
        """
        Synchronize the catalog.
        This method uses the json:api to fetch the product variants and
        updates the 'response_data' field with the response.

        Returns:
            dict: An action to open a form view of the current record.
        """
        # Get a json:api session
        # self.ensure_one()
        # Example: Synchronize all product templates
        # Adjust the domain based on your needs

        self.env["product.product"].search([], limit=1).action_synchronize_product()

        return {
            "name": "Log Catalog",
            "type": "ir.actions.act_window",
            "res_model": "aks.sync.error",
            "view_mode": "list,form",
            "context": self.env.context,
        }

    def _handle_webhook(self, type, id, status):
        """
        Handle a webhook from AKS.

        async methods call to process objects methods

        """

        _logger.info(f"Webhook received : {type}: {id}")

        uid = self.res_users_id or self.env.user.Superuser
        # "external_order.created" "order.brand_accepted",
        #             "order.brand_rejected",: nothing to do
        if type in [
            "external_order.awaiting_fulfillment",
            "external_order.cancelled",
            "external_order.shipped",
            "external_order.arrived",
        ]:
            picking = self.env["stock.picking"].with_user(uid).search([("aks_master_order", "=", id)])
            if picking:
                picking.get_last_status()
        elif type == "order.brand_created":
            self.env["sale.order"].with_user(uid).create_from_aks(id)
        elif type in [
            "order.shipped",
            "order.shipment_received",
            "order.shipment_refused",
            "order.brand_paid",
            "order.billing_address_updated",
            "order.shipping_address_updated",
            "order.cancelled",
            "order.brand_accepted_reverted",
        ]:
            self.env["sale.order"].with_user(uid).set_status(id, status)
        elif type in [
            "order.shipping_labels_generated",
            "order.shipping_labels_generated_reverted",
        ]:
            self.env["sale.order"].with_user(uid).set_status(id, status)
            pickings = self.env["sale.order"].with_user(uid).search([("aks_id", "=", id)]).picking_ids
            for picking in pickings:
                picking.carrier_id.aks_get_shipping_overview(picking)
        else:
            pass

    def run_cron_list_aks_orders(self):
        """
        Watch for new orders in AKS and import them into Odoo.
        """
        for backend in self.search([]):
            backend.with_context(default_company_id=backend.company_id.id).action_trigger_list_aks_orders()

    def action_trigger_list_aks_orders(self):
        """
        Helper method to trigger action_list_aks_orders on the SaleOrder model.
        """
        self.env["sale.order"].action_list_aks_orders()
        return {
            "type": "ir.actions.client",
            "tag": "reload",
            "params": {"message": "AKS Orders synchronization initiated."},
        }

    def action_trigger_sync_aks_fc_stock(self):
        """
        Trigger synchronization of stock levels from AKS FC
        for all product variants.
        """
        # self.ensure_one()
        product_variants = self.env["product.product"].search([])

        for variant in product_variants:
            if variant.is_aks_exported and variant.aks_id:
                variant.action_retrieve_fc_stock()
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": "AKS FC Stock Sync",
                "message": "AKS FC Stock Successfully Imported",
                "sticky": False,
            },
        }

    def action_trigger_sync_aks_stock(self):
        """
        Trigger synchronization of stock levels from Odoo to AKS
        for product variants.
        """
        # self.ensure_one()
        # Example: Synchronize all product templates
        # Adjust the domain based on your needs
        product_variants = self.env["product.product"].search([])
        for variant in product_variants:
            variant.action_update_stock()

        # Return a client action to provide UI feedback or reload the view.
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": "AKS Stock Sync",
                "message": "AKS Stock Successfully Exported",
                "sticky": False,
            },
        }

    def action_view_create_order(self):
        """
        Create a test order in AKS.
        """
        return {
            "name": "Create Test Order",
            "type": "ir.actions.act_window",
            "res_model": "aks.test.order.wizard",
            "view_mode": "form",
            "target": "new",
        }

    def fetch_aks_app_id(self):
        """
        Fetch the application ID for the target application by name or another identifying attribute.
        https://ankorstore.github.io/api-docs/#tag/Webhooks/How-to-create-a-new-webhook-subscription
        """
        try:
            client = self.get_instance()
            response = client.list_ask_apps()
            apps = response.get("data", [])

            # Loop through applications to find the target one
            for app in apps:
                if app["attributes"]["name"] == self.aks_app_name:
                    aks_app_id = app["id"]
                    self.aks_app_id = aks_app_id
                    return aks_app_id

            # Handle case where the application is not found
            _logger.warning("Target application not found.")
            return None

        except Exception:
            error_message = "Connection failed, please check you used the same application name in Odoo and Ankorstore, and copied and pasted the Client ID and Client secret properly"
            _logger.error(error_message)
            raise ValidationError(error_message)

    def action_subscribe_to_all_webhooks(self):
        """
        Subscribes to all defined webhook events for the Ankorstore platform.
        """

        self.ensure_one()
        if not self.odoo_webhook_base_url or not self.use_webhooks:
            raise UserError("Please confirm the use of webhooks and configure the Odoo Webhook Base URL before creating subscriptions.")

        aks_app_id = self.fetch_aks_app_id()
        if not aks_app_id:
            raise UserError(
                "Connection failed, please check you used the same application name in Odoo and Ankorstore, and copied and pasted the Client ID and Client secret properly."
            )

        failed_subs = []
        successful_subs = []
        client = self.get_instance()

        existing_webhooks = self.env["backend.aks.webhook"].search([("backend_id", "=", self.id)])
        existing_event_codes = existing_webhooks.mapped("event_type")

        for event_code, event_name in WEBHOOK_EVENTS:
            if event_code in existing_event_codes:
                _logger.info(f"Webhook subscription for {event_name!r} already exists. Skipping...")
                continue

            webhook_url = self.odoo_webhook_base_url
            try:
                response_data = client.subscribe_to_webhooks(aks_app_id, webhook_url, [event_code])
                _logger.info(f"Webhook subscription for {event_name!r} created successfully.")

                subscription_id = response_data.get("data", {}).get("id", "")

                # Create or update a Webhook record
                webhook = self.env["backend.aks.webhook"].search(
                    [("event_type", "=", event_code), ("backend_id", "=", self.id)],
                    limit=1,
                )

                if webhook:
                    webhook.write(
                        {
                            "url": webhook_url,
                            "subscription_active": True,
                            "subscription_id": subscription_id,
                        }
                    )
                else:
                    self.env["backend.aks.webhook"].create(
                        {
                            "event_type": event_code,
                            "url": webhook_url,
                            "subscription_active": True,
                            "subscription_id": subscription_id,
                            "backend_id": self.id,
                        }
                    )

                successful_subs.append((event_code, event_name))

            except Exception as e:
                _logger.error(f"Failed to create webhook subscription for {event_name!r}: {e}")
                failed_subs.append((event_code, event_name))

        if failed_subs:
            error_message = "Failed to create subscriptions for the following events: " + ", ".join([name for code, name in failed_subs])
            _logger.error(error_message)

        if successful_subs:
            success_message = "Successfully created subscriptions for the following events: " + ", ".join([name for code, name in successful_subs])
            _logger.info(success_message)

    def action_unsubscribe_webhooks(self):
        """
        Delete all webhook subscriptions.
        """
        self.aks_webhook_ids.action_unsubscribe_webhooks()
        self.aks_webhook_ids.search([("subscription_active", "=", False)]).unlink()

    def action_activate_connection(self):
        """
        Activate the connection with Ankorstore.
        in order:
        - whoiam
        - subscribe to all webhooks
        - integration activate
        """
        self.active = True
        self.action_whoiam()
        self.action_subscribe_to_all_webhooks()
        self.action_integration_activate()
        self.connected = True
        return {"type": "ir.actions.client", "tag": "soft_reload"}

    def action_deactivate_connection(self):
        """
        Deactivate the connection with Ankorstore.
        in order:
        - integration deactivate
        """
        self.action_integration_deactivate()
        self.action_unsubscribe_webhooks()
        self.connected = False
        self.active = False
        return {"type": "ir.actions.client", "tag": "soft_reload"}

    # def action_help_url_1(self):
    #     return {
    #         'type': 'ir.actions.act_url',
    #         'url': self.help_url_1,
    #         'target': 'new',
    #     }

    def action_help_url_2(self):
        return {
            "type": "ir.actions.act_url",
            "url": "https://www.loom.com/share/79ad0511e68f4c5aa1e1596e6d40d331?sid=0c04b564-e486-4abc-a95e-8f8deb07d8b2",
            "target": "new",
        }


class Webhook(models.Model):
    """
    Ankorstore Webhook Subscription Model.
    """

    _name = "backend.aks.webhook"
    _description = "Ankorstore Webhook Subscription"
    _rec_name = "event_type"

    event_type = fields.Selection(selection=WEBHOOK_EVENTS, string="Event Type")
    url = fields.Char("Webhook URL")
    subscription_active = fields.Boolean("Subscription Active", default=True)
    subscription_id = fields.Char("Subscription ID")
    backend_id = fields.Many2one("backend.aks", string="Backend", ondelete="cascade")

    def action_unsubscribe_webhooks(self):
        """
        Action to unsubscribe from selected webhook subscriptions.
        """
        for record in self:
            if record.subscription_id:
                try:
                    client = record.env["backend.aks"].get_instance()
                    client.unsubscribe_from_webhook(record.subscription_id)
                    self.subscription_active = False
                except Exception as e:
                    _logger.error(f"Failed to unsubscribe from webhook {record.event_type!r}: {e}")
        return True
