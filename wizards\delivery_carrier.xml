<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="choose_delivery_carrier_view_form_inherit" model="ir.ui.view">
        <field name="name">choose.delivery.carrier.form</field>
        <field name="model">choose.delivery.carrier</field>
        <field name="inherit_id" ref="delivery.choose_delivery_carrier_view_form"/>
        <field name="arch" type="xml">
        <xpath expr="//button[@name='update_price']" position="attributes">
                <attribute name="invisible">delivery_type in ['fixed','base_on_rule','aks']</attribute>
        </xpath>
         <form> 
            <xpath expr="//field[@name='carrier_id']" position="after">
                <group string="Ankorstore" name="aks_group" colspan="2" invisible="delivery_type != 'aks'">
                   <field name="calculate_parcel_method"/>              
                   <field name="is_hazardous_goods" widget="boolean_toggle" string="Content hazardous goods" invisible="1"/>                  
                    <group string="Estimate Package" name="package_auto" colspan="2" invisible="calculate_parcel_method != 'automatic'">
                        <field name="package_id" />
                    </group>
                    <group string="Entry package" name="package_manuel" colspan="2" invisible="calculate_parcel_method == 'automatic'">
                        <field name="package_line_ids" nolabel="1" colspan="2">
                            <list>  <!-- tree became list in v18 -->
                                <field name="package_id"/>
                                <field name="parcel_number"/>
                                <field name="parcel_weight"/>
                            </list>
                        </field>
                    </group>                    
                </group>      
            </xpath>      
                       
            <group string="Quotes Results" name="lines" invisible="delivery_type != 'aks'">
                <button string="Get Quotes" type="object" name="get_lines_quote" class="btn-primary"/>                
                    <field name="line_ids" nolabel="1" colspan="2">
                        <list>  <!-- tree became list in v18 -->
                            <field name="quote_uuid" />
                            <field name="service_commercial_name" />
                            <field name="amount" />                                
                            <button class="btn-primary"
                                    name="update_quote"
                                    string="Select"
                                    type="object"
                                    confirm="Confirm selected quote ?"/>
                        </list>
                    </field>
            </group>         
        </form>

        </field>
    </record>
</odoo>