from odoo import SUPERUSER_ID, api
 
 
def migrate(cr, version):
 
    env = api.Environment(cr, SUPERUSER_ID, {})

    backends = env["backend.aks"].search([])

    for backend in backends:
        field_to_migrate = "aks_fullilable_id"
        if field_to_migrate in env["product.product"].fields_get():
            products_to_migrate = env["product.product"].search([("aks_fullilable_id", "not in", (<PERSON><PERSON><PERSON>, None))])
            if products_to_migrate:
                for product in products_to_migrate:
                    product.aks_fulfillable_id = product.aks_fullilable_id
        env["product.product"].action_synchronize_product()