
<odoo>
    <!-- Product Product Tree View -->
    <record id="view_product_product_tree_inherit" model="ir.ui.view">
        <field name="name">product.product.tree.inherit</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='default_code']" position="after">
                <field name="is_aks_exported" optional="show"/>
            </xpath>
        </field>
    </record>

    <!-- Product Product Form View -->
    <record id="product_normal_form_view_aks" model="ir.ui.view">
        <field name="name">product.product.view.form.inherit.aks</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='type']" position="after">  <!-- "//field[@name='detailed_type']" doesn't exist anymore, replace by type -->
                    <field name="is_aks_exported"/>
                    <field name="is_active_on_aks"/>
                    <field name="aks_id"/>
            </xpath>
            
            <xpath expr="//form[1]/sheet[@name='product_form']/notebook[1]" position="inside">
                <page string="Ankorstore" name="aks_info">
                <group name="aks_info_group">
                    <group name="aks_info_group_left">
                    <field name="aks_name" force_save="1" readonly="True"/>
                    <field name="aks_retail_price" force_save="1" readonly="True"/>
                    <field name="aks_wholesale_price" force_save="1" readonly="True"/>
                    <field name="aks_original_wholesale_price" force_save="1" readonly="True"/>
                    <field name="aks_last_synchronization" force_save="1" readonly="True"/>
                    <field name="aks_archived_at"/>
                    </group>
                    <group name="aks_info_group_right">
                    <field name="is_aks_always_in_stock"/>
                    <field name="aks_available_quantity" force_save="1" readonly="True"/>
                    <field name="aks_reserved_quantity" force_save="1" readonly="True"/>
                    <field name="aks_stock_quantity" force_save="1" readonly="True"/>
                    </group>
                </group>
                </page>
            </xpath>
        </field>
   </record>

   <record id="product_template_only_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.only.form.view.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_tag_ids']" position="after">
                <field name="is_aks_exported"/>
                <field name="display_variants_exported"/>
              </xpath>
        </field>
    </record>
</odoo>