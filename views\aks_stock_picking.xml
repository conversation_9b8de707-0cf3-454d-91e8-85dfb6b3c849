<odoo>
    <record id="view_stock_picking_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.form</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
             <xpath expr="//button[@name='print_return_label']" position="after">
             <button string="AKS Confirm Shippping" name="button_aks_confirm_custom_ship" type="object" invisible="not show_aks_confirm_custom_ship"/>
            </xpath>
            <xpath expr="//form[1]/sheet[1]/group[1]/group[1]/div[1]" position="before">
                <field name="aks_master_order" invisible="not aks_master_order"/>
                <field name="show_aks_confirm_custom_ship" invisible="1"/>
                <field name ="hide_aks_picking" invisible="1"/>
            </xpath>
            <xpath expr="//form[1]/sheet[1]/group[1]/group[1]/field[@name='location_id'][2]" position="after">
                <field name="is_aks_fc" invisible="True"/>
                <field name="aks_id" invisible="not aks_id"/>
                <field name="aks_latest_quote" invisible="carrier_id.type != 'aks'"/>
            </xpath>
            <xpath expr="//form[1]/sheet[1]/group[1]/group[2]/label[1]" position="before">
                <field name="aks_sync_status" invisible="not is_aks_fc" />
            </xpath>
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="fulfillableid" optional="show"/>
            </xpath>
            <xpath expr="//button[@name='action_assign']" position="attributes">
                <attribute name="invisible">not show_check_availability or hide_aks_picking</attribute>
              </xpath>
              <xpath expr="//form[1]/header[1]/button[@name='button_validate']" position="attributes">
                <attribute name="invisible">state in ["draft", "confirmed", "done", "cancel"] or hide_aks_picking</attribute>
              </xpath>
              <xpath expr="//form[1]/header[1]/button[@name='button_validate'][2]" position="attributes">
                <attribute name="invisible">state in ["waiting", "assigned", "done", "cancel"] or hide_aks_picking</attribute>
              </xpath>  <!--
              <xpath expr="//button[@name='action_open_label_type']" position="attributes">
                <attribute name="invisible">hide_aks_picking</attribute>
              </xpath> button action_open_label_type became an ir.actions.server in v18 -->
              <xpath expr="//button[@name='do_print_picking']" position="after">
                <button string="Print Labels" name="action_open_label_type" type="object" invisible="hide_aks_picking"/>
              </xpath>
              <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="invisible">state not in ["assigned", "confirmed", "draft", "waiting"] or hide_aks_picking</attribute>
              </xpath>
        </field>
    </record>

    <record id="view_warehouse_inherit" model="ir.ui.view">
        <field name="name">stock.warehouse.form</field>
        <field name="model">stock.warehouse</field>
        <field name="inherit_id" ref="stock.view_warehouse"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="is_aks_fc"/>
              </xpath>
        </field>
    </record>
</odoo>