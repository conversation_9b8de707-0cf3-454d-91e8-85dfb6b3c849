<odoo>
    <!-- Sale Order Form View INTERNAL ORDER -->
    <record id="view_order_form_aks" model="ir.ui.view">
        <field name="name">sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">

            <xpath expr="//form[1]/header[1]/field[@name='state']" position="after">
                <button string="Reject AKS Order" name="action_reject_aks_orders" type="object" invisible="not aks_id or state !='draft'" />
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="before">
                <field name="last_aks_status" invisible="not aks_id" force_save="1" readonly="True"/>
                <field name="client_order_ref" string="Customer Reference" invisible="not aks_id" force_save="1" readonly="True"/>
                <!-- <field name="aks_last_synchronization" invisible="not aks_id"/> -->
                <field name="aks_external_master_order" invisible="not aks_external_master_order"/>
                <field name="aks_id" invisible="not aks_id" force_save="1" readonly="True"/>
                <field name="aks_mode" force_save="1" readonly="state not in ['draft','sent']"/>
                
            </xpath>

            <xpath expr="//notebook" position="inside">
                <page string="Ankorstore Fees" invisible="not aks_id">
                    <field name="aks_fees">
                        <list>  <!-- tree became list in v18 -->
                            <field name="type"/>
                            <field name="vatRate" optional="show"/>
                            <field name="amount" optional="show"/>
                            <field name="currency" optional="show"/>
                       </list>
                    </field>
                </page>
            </xpath>

            <xpath expr="//form[1]/header[1]/button[@name='action_quotation_send']" position="attributes">
                <attribute name="invisible">aks_id</attribute>
              </xpath>
              <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="invisible">state not in ["draft", "sent", "sale"] or not id or locked or aks_id</attribute>
              </xpath>
              <xpath expr="//field[@name='validity_date']" position="before">
                <field name="aks_reject_reason" invisible="aks_reject_reason not in [&quot;BRAND_ALREADY_HAS_CUSTOMER_IN_THE_AREA&quot;, &quot;BRAND_CANNOT_DELIVER_TO_THE_AREA&quot;, &quot;BRAND_HAS_EXCLUSIVE_DISTRIBUTOR_IN_THE_REGION&quot;, &quot;BUYER_NOT_A_RETAILER&quot;, &quot;ORDER_ITEMS_PRICES_INCORRECT&quot;, &quot;PAYMENT_ISSUES_WITH_RETAILER&quot;, &quot;PREPARATION_TIME_TOO_HIGH&quot;, &quot;PRODUCT_OUT_OF_STOCK&quot;, &quot;OTHER&quot;, &quot;RETAILER_VAT_NUMBER_MISSING&quot;, &quot;RETAILER_NOT_GOOD_FIT_FOR_BRAND&quot;, &quot;RETAILER_AGREED_TO_DO_CHANGES_TO_ORDER&quot;, &quot;PURCHASE_NOT_FOR_RESALE&quot;]"/>
              </xpath>
              <xpath expr="//form[1]/sheet[1]/group[@name='sale_header']/group[@name='order_details']/field[@name='pricelist_id']" position="attributes">
                <attribute name="invisible">aks_id</attribute>
              </xpath>
              <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='order_lines']/div[@name='so_button_below_order_lines']/button[@name='action_open_delivery_wizard']" position="attributes">
                <attribute name="invisible">is_all_service or not order_line or delivery_set or aks_mode == "FFA"</attribute>
              </xpath>
              <xpath expr="//page[@name='optional_products']" position="attributes">
                <attribute name="invisible">state not in ["draft", "sent"] or aks_id</attribute>
              </xpath>
        </field>
   </record>
</odoo>