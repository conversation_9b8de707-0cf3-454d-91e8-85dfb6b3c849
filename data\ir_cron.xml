<odoo>
    <!-- numbercall and doall doesn't exist anymore in model ir.cron -->
    <record forcecreate="True" id="aks_sync_stock_from_odoo" model="ir.cron">
        <field name="name">Ankorstore Sync AKS Stock Level From Odoo</field>
        <field name="model_id" ref="model_product_product"/>
        <field name="state">code</field>
        <field name="code">model.cron_update_stock()</field>
        <field eval="False" name="active"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>    <!-- <field name="numbercall">-1</field> - <field eval="False" name="doall"/> -->
    </record>

    <record forcecreate="True" id="aks_sync_catalog" model="ir.cron">
        <field name="name">Ankorstore Sync Catalog</field>
        <field name="model_id" ref="model_product_product"/>
        <field name="state">code</field>
        <field name="code">model.cron_synchronize_product()</field>
        <field eval="False" name="active"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>     <!-- <field name="numbercall">-1</field> - <field eval="False" name="doall"/> -->
    </record>

    <record forcecreate="True" id="aks_sync_stock_from_aks_fc" model="ir.cron">
        <field name="name">Ankorstore Sync Odoo Stock Level From AKS FC</field>
        <field name="model_id" ref="model_product_product"/>
        <field name="state">code</field>
        <field name="code">model.cron_retrieve_fc_stock()</field>
        <field eval="False" name="active"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>     <!-- <field name="numbercall">-1</field> - <field eval="False" name="doall"/> -->
    </record>
        <record forcecreate="True" id="aks_sync_orders" model="ir.cron">
        <field name="name">Ankorstore Sync Orders From AKS FC</field>
        <field name="model_id" ref="model_backend_aks"/>
        <field name="state">code</field>
        <field name="code">model.run_cron_list_aks_orders()</field>
        <field eval="False" name="active"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>     <!-- <field name="numbercall">-1</field> - <field eval="False" name="doall"/> -->
    </record>

    <record forcecreate="True" id="aks_sync_orders_status" model="ir.cron">
        <field name="name">Ankorstore Sync All Orders Status</field>
        <field name="model_id" ref="model_sale_order"/>
        <field name="state">code</field>
        <field name="code">model.cron_get_last_status()</field>
        <field eval="False" name="active"/>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>     <!-- <field name="numbercall">-1</field> - <field eval="False" name="doall"/> -->
    </record>
 </odoo>
