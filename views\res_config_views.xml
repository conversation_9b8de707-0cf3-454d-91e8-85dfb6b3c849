<odoo>
    <record id="res_config_settings_view_form_connector_ankorstore" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.connector_ankorstore</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="45"/>
        <field name="inherit_id" ref="sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">

            <!-- Placing the validations settings in the Sales Connector settings -->
            <xpath expr="//block[@id='connectors_setting_container']" position="inside">
                <setting string="Ankorstore Connector" help="Import Ankorstore orders and manage fulfillment">
                    <button type="action" name="%(connector_ankorstore.action_backend_aks_tree)d" string="Manage API Credentials" icon="oi-arrow-right" class="btn-link"/>
                </setting>

            </xpath>
        </field>
    </record>

</odoo>
