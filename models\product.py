# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
import logging
from datetime import datetime

from odoo import _, api, fields, models
from odoo.addons.connector_ankorstore.aks.jsonapi_client import Filter
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)
# NOT IMPLEMENTED : LOT, SERIAL, OWNER, PACKAGE, ATTRIBUTES, ATTRIBUTES_VALUES cause AKS API does not support these features


class ProductTemplate(models.Model):
    _inherit = "product.template"

    is_aks_exported = fields.Boolean(string="Is the product exported to AKS", compute="_is_aks_exported", store=True, default=False)
    display_variants_exported = fields.Char(string="Are all the variants exported to AKS", compute="_display_all_variants_exported", store=True, default=False)

    @api.depends("product_variant_ids.is_aks_exported")
    def _is_aks_exported(self):
        """Check if the product is exported to AKS"""
        for product in self:
            product.is_aks_exported = False
            if any(product.product_variant_ids.mapped("is_aks_exported")):
                product.is_aks_exported = True

    @api.depends("product_variant_ids.is_aks_exported")
    def _display_all_variants_exported(self):
        """
        Check and set a message indicating the export status of variants
        to AKS. Sets a message on the product template indicating if all, some,
        or no variants have been synchronized to AKS.
        """
        for product in self:
            # Filter variants of the product that have been exported to AKS
            exported_variants = product.product_variant_ids.filtered("is_aks_exported")  # noqa
            # Count the total number of variants for the product
            total_variants = len(product.product_variant_ids)

            # If no variants have been exported, set the message accordingly
            if not exported_variants:
                product.display_variants_exported = _("No variants are synchronized to AKS")
                continue

            # Special message for products with only one variant
            if total_variants == 1:
                message = "This product is synchronized to AKS (no variants)"
            # Determine the message based on the count of exported variants
            elif len(exported_variants) == total_variants:
                message = "All variants are synchronized to AKS"
            else:
                message = "Some variants are synchronized to AKS (see logs) "

            product.display_variants_exported = message


class ProductProduct(models.Model):
    _inherit = "product.product"

    aks_id = fields.Char(string="Ankorstore UUID", copy=False)
    aks_name = fields.Char(string="AKS Name")
    is_aks_exported = fields.Boolean(string="Is the product exported to AKS", compute="_is_aks_exported", store=True, default=False)
    aks_product_master_id = fields.Char(string="AKS Product Master ID", copy=False)
    aks_fulfillable_id = fields.Char(string="AKS Fulfillable ID", copy=False)
    aks_sku = fields.Char(string="AKS SKU", copy=False)
    aks_ian = fields.Char(string="AKS IAN", copy=False)
    aks_created_at = fields.Datetime(string="AKS Created At")
    aks_updated_at = fields.Datetime(string="AKS Updated At")
    aks_archived_at = fields.Datetime(string="AKS Archived At")
    aks_retail_price = fields.Float(string="AKS Retail Price")
    aks_wholesale_price = fields.Float(string="AKS Wholesale Price")
    aks_original_wholesale_price = fields.Float(string="AKS Original Wholesale Price")
    aks_available_quantity = fields.Integer(string="AKS Available Quantity")
    aks_reserved_quantity = fields.Integer(string="AKS Reserved Quantity")
    aks_stock_quantity = fields.Integer(string="AKS Stock Quantity")
    is_aks_always_in_stock = fields.Boolean(string="AKS Is Always In Stock")
    is_active_on_aks = fields.Boolean(string="AKS Is Active", compute="_is_active_on_aks", store=True, default=False)
    aks_last_synchronization = fields.Datetime(string="AKS Last Synchronization")

    @api.depends("aks_id")
    def _is_aks_exported(self):
        """Check if the product is exported to AKS"""
        for product in self:
            product.is_aks_exported = False
            if product.aks_id:
                product.is_aks_exported = True

    @api.depends("aks_archived_at", "aks_id")
    def _is_active_on_aks(self):
        """Check if the product is active on AKS"""
        for product in self:
            product.is_active_on_aks = False
            if not product.aks_archived_at and product.aks_id:
                product.is_active_on_aks = True

    def get_aks_product(self, backend=None):
        """
        Fetch a product variant's details from Ankorstore
        using the product's AKS ID.
        """
        self.ensure_one()
        if not self.aks_id:
            raise UserError(_(f"AKS ID is missing for the product {self.name}."))
        if not backend:
            backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()

        try:
            aks_product = session.get(f"product-variants/{self.aks_id}")
            if not aks_product:
                _logger.error(f"Failed to retrieve AKS product {self.aks_id}")
                return None
            return aks_product
        except Exception as e:
            _logger.error(_(f"Error fetching product {self.aks_id} from AKS: {e}"))
            return None

    def cron_synchronize_product(self):
        """
        Cron job to synchronize product variants from Ankorstore to Odoo for each backend.
        """
        backends = self.env["backend.aks"].search([])
        for backend in backends:
            instance = self.env["backend.aks"].with_context(default_company_id=backend.company_id.id).get_instance()
            self.action_synchronize_product(backend=instance)

    def action_synchronize_product(self, backend=None):
        """
        Synchronize product variants from Ankorstore to Odoo.
        Iterates through product variants from Ankorstore, updating matching
        Odoo products or logging warnings for unmatched products.
        """
        to_flush = self.env["product.product"].search([("aks_id", "!=", False)])
        to_flush.write({"aks_id": False, "aks_fulfillable_id": False})

        if not backend:
            backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        try:
            aks_products = session.iterate("product-variants")
        except Exception as e:
            error_message = _(f"Error fetching products from AKS: {e}")
            self.env["aks.sync.error"]._logme(
                model="product.product",
                operation="action_synchronize_product",
                name="ALL",
                error_message=error_message,
            )
        for aks_product in aks_products:
            matching_product = False
            # If no ID match, try matching by SKU
            if aks_product.sku:
                matching_product = self.search([("default_code", "=", aks_product.sku)], limit=1)

            # If no SKU match, try matching by barcode
            if not matching_product and aks_product.ian:
                matching_product = self.search([("barcode", "=", aks_product.ian)], limit=1)

            # Update found product with Ankorstore details
            if matching_product:
                matching_product.sudo().write(
                    {
                        "aks_id": aks_product.id,
                        "aks_ian": aks_product.ian,
                        "aks_name": aks_product.name,
                        "aks_created_at": self._convert_aks_date(aks_product.createdAt),
                        "aks_updated_at": self._convert_aks_date(aks_product.updatedAt),
                        "aks_archived_at": self._convert_aks_date(aks_product.archivedAt),
                        "aks_retail_price": aks_product.retailPrice / 100,
                        "aks_wholesale_price": aks_product.wholesalePrice / 100,
                        "aks_original_wholesale_price": aks_product.originalWholesalePrice / 100,
                        "aks_available_quantity": aks_product.availableQuantity,
                        "aks_reserved_quantity": aks_product.reservedQuantity,
                        "aks_stock_quantity": aks_product.stockQuantity,
                        "is_aks_always_in_stock": aks_product.isAlwaysInStock,
                        "aks_last_synchronization": datetime.now(),
                        "aks_fulfillable_id": aks_product.fulfillableId if aks_product.fulfillableId else False,
                    }
                )
            else:
                # Log warning if no matching Odoo product is found
                if not aks_product.archivedAt:
                    error_message = f"Product not found in Odoo: {aks_product.name} " f"SKU: {aks_product.sku} IAN: {aks_product.ian}"
                    self.env["aks.sync.error"]._logme(
                        model="product.product",
                        operation="action_synchronize_product",
                        name=aks_product.name,
                        error_message=error_message,
                    )
                    _logger.warning(error_message)

    @api.model
    def _convert_aks_date(self, date):
        """Convert AKS date to odoo date
        date format : 2021-03-01T10:00:00+01:00"""

        if not date:
            return False
        datetime_object = datetime.strptime(date, "%Y-%m-%dT%H:%M:%S%z")
        formatted_time = datetime_object.strftime("%Y-%m-%d %H:%M:%S")
        return formatted_time

    @api.model
    def _get_quantity_for_aks(self, allowed_location_ids):
        """
        Calculate the product's available quantity for Ankorstore based on
        specified locations.
        """
        # using standard odoo method , # TODO: add filter domain by allowed wh/locations
        # see src/odoo/addons/stock/models/product.py

        qty = self.with_context(location=allowed_location_ids)._compute_quantities_dict(
            self._context.get("lot_id"), self._context.get("owner_id"), self._context.get("package_id"), self._context.get("from_date"), self._context.get("to_date")
        )
        # Prefer 'free_qty' (quantity reserved include) but fallback to 'qty_available'
        qty_to_send = qty[self.id]["free_qty"]
        if qty_to_send == 0:
            qty_to_send = qty[self.id]["qty_available"]
        if qty_to_send < 0:
            qty_to_send = 0
        return qty_to_send

    def update_aks_stock(self, backend=None):
        """
        Export the current stock levels of a product to AKS.
        This method sends a PATCH request to AKS to update the stock quantity
        for a product variant identified by its `aks_id`. It first calculates the
        available quantity based on allowed locations, then prepares and sends
        the data to AKS.
        see https://www.public.ankorstore-sandbox.com/api/v1/product-variants/{productVariant}/stock
        """
        self.ensure_one()

        if not self.aks_id:
            # Exit if the product does not have an AKS identifier
            return False
        if not backend:
            backend = self.env["backend.aks"].get_instance()

        path = f"{backend.base_url}/api/v1/product-variants/{self.aks_id}/stock"

        session = backend.get_session()

        # Fetch parameters for allowed locations for stock sync
        params = self.env["backend.aks"].get_params()

        # Calculate the quantity available for AKS sync
        qty = self._get_quantity_for_aks(params.aks_sync_allowed_location_ids.ids)

        # Prepare data for PATCH request
        dict_to_send = self._prepare_aks_data(qty)

        try:
            # Attempt to update the product stock on AKS
            session.http_request("PATCH", path, dict_to_send)
            _logger.info(f"Stock updated for product {self.name} : {qty}")
            return True

        except Exception as e:
            # Log errors and return False to indicate failure
            error_message = _(f"Error while updating stock for product {self.name} : {e}")
            self.env["aks.sync.error"]._logme(model="product.product", operation="update_aks_stock", name=self.name, error_message=error_message)

            _logger.warning(error_message)
            return False

    def _prepare_aks_data(self, qty):
        """Prepare data for AKS"""
        if self.is_aks_always_in_stock:
            attr = {"isAlwaysInStock": True}
        else:
            attr = {"isAlwaysInStock": False, "stockQuantity": qty}
        data = {"data": {"type": "product-variants", "id": self.aks_id, "attributes": attr}}
        return data

    def cron_update_stock(self):
        """
        Cron job to update stock levels on Ankorstore for all products marked as exported to AKS.
        """
        backends = self.env["backend.aks"].search([])
        for backend in backends:
            instance = self.env["backend.aks"].with_context(default_company_id=backend.company_id.id).get_instance()
            products = self.search([("is_aks_exported", "=", True)])
            for product in products:
                product.action_update_stock(backend=instance)

    def action_update_stock(self, backend=None):
        """
        Main action Update stock for all products
        Iterates over products marked as exported to Ankorstore and updates
        their stock levels on Ankorstore.
        """
        # Filter products that are marked as exported to Ankorstore
        exported_products = self.filtered("is_aks_exported")

        # Log if no products are found for update
        if not exported_products:
            _logger.info(_("No exported products found for AKS stock update."))
            return

        # Initialize a recordset to track products for which the update fails
        failed_updates = self.env["product.template"]  # TODO: product.product
        for product in exported_products:
            try:
                # Attempt to update the stock on Ankorstore for each product
                success = product.update_aks_stock(backend)
                # If the update was not successful, add to the failed list
                if not success:
                    failed_updates += product
            except Exception as e:
                # Log any exceptions encountered during the update
                _logger.error(f"Error updating AKS stock for {product.display_name}: {e}")
                failed_updates += product

        # Log the names of products for which the stock update failed
        if failed_updates:
            failed_names = ", ".join(failed_updates.mapped("display_name"))
            _logger.info(f"Failed to update AKS stock for: {failed_names}")
            # Iterating over each failed product to post a message
            for failed_product in failed_updates:
                failed_product.message_post(body=_(f"Failed to update AKS stock for product {failed_product.display_name}."))

    def cron_retrieve_fc_stock(self):
        """
        Cron job to retrieve stock levels from Ankorstore Fulfillment Center (FC)
        for all products marked as exported to Ankorstore.
        """
        backends = self.env["backend.aks"].search([])
        for backend in backends:
            instance = self.env["backend.aks"].with_context(default_company_id=backend.company_id.id).get_instance()
            products = self.search([("is_aks_exported", "=", True)])
            for product in products:
                product.action_retrieve_fc_stock(backend=instance)

    def action_retrieve_fc_stock(self, backend=None):
        """
        Iterates over products marked as exported to Ankorstore and
        synchronizes their stock levels from the
        Ankorstore Fulfillment Center (FC).
        """
        # Filter products that are marked as exported to Ankorstore
        exported_products = self.filtered("is_aks_exported")
        updated_products = []

        # Log if no products are found for update
        if not exported_products:
            _logger.info(_("No exported products found for FC stock retrieval."))
            return
        if not backend:
            backend = self.env["backend.aks"].get_instance()
        # Search for the stock location marked as AKS FC
        aks_fc_location_id = self.env["stock.location"].search([("is_aks_fc", "=", True)], limit=1)
        if not aks_fc_location_id:
            self.env["aks.sync.error"]._logme(model="product.product", operation="set_aks_fc_stockc", name=self.name, error_message="AKS FC location not found in Odoo.")
            raise UserError("AKS FC location not found in Odoo.")
        for product in exported_products:
            try:
                product.set_aks_fc_stock(backend, aks_fc_location_id)
                updated_products.append(product)
            except Exception as e:
                _logger.error(f"Failed to retrieve FC stock for product {product.display_name}: {e}")
                product.message_post(body=_(f"Error retrieving FC stock: {e}"))

        return updated_products

    def set_aks_fc_stock(self, backend=None, aks_fc_location_id=None):
        """
        Synchronize stock levels from Ankorstore Fulfillment Center (AKS FC)
        to a specific stock location in Odoo designated as AKS FC.
        """
        self.ensure_one()
        if not backend:
            backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        stock_id = self.get_aks_product(backend)
        company_id = self.env.context["default_company_id"] if self.env.context.get("default_company_id") else self.env.company.id

        if stock_id and stock_id.resource.fulfillableId:
            fulfillable_id = stock_id.resource.fulfillableId
            str_filter = f"fulfillableIds[]={fulfillable_id}"
            include = backend.inclusion("lots")
            filter = Filter(str_filter)
            modif = filter + include
            # https://www.public.ankorstore-sandbox,.com/api/v1/fulfillment/fulfillable?fulfillableIds[]=1eec3fa4-b6fc-617a-aac0-02f1999cea56
            try:
                fulfillments = session.get("fulfillment/fulfillable", modif)
                if not fulfillments:
                    self.message_post(body="This product is currently not fulfillable.")
                    return False
                if fulfillments and fulfillments.resource.unitQuantity:
                    pcb = fulfillments.resource.unitQuantity / fulfillments.resource.batchQuantity
                    if fulfillments.resource.relationships.lots:
                        if self.tracking not in ["lot", "serial"]:
                            _logger.error(f"Product {self.display_name} is not tracked by lot or serial")
                            self.env["aks.sync.error"]._logme(
                                model="product.product", operation="set_aks_fc_stockc", name=self.name, error_message="Product is not tracked by lot or serial"
                            )
                            return False

                        lots = fulfillments.resource.relationships.lots.resources
                        for lot in lots:
                            find = self.env["stock.lot"].search([("name", "=", lot.lotNumber), ("product_id", "=", self.id), ("company_id", "=", company_id)])
                            if find:
                                # clear inventory
                                res = self.env["stock.quant"].search(
                                    [("product_id", "=", self.id), ("location_id", "=", aks_fc_location_id.id), ("lot_id", "=", find.id)]
                                )
                                if res:
                                    res.action_clear_inventory_quantity()
                                # compare qty
                                qty = self.with_context(location=aks_fc_location_id.id)._compute_quantities_dict(
                                    find.id,
                                    self._context.get("owner_id"),
                                    self._context.get("package_id"),
                                )
                                if qty[self.id]["free_qty"] != lot.availableQuantity * pcb:
                                    (
                                        self.env["stock.quant"]
                                        .with_context(inventory_mode=True)
                                        .sudo()
                                        .create(
                                            {
                                                "product_id": self.id,
                                                "location_id": aks_fc_location_id.id,
                                                "lot_id": find.id,
                                                "inventory_quantity": lot.availableQuantity * pcb,
                                            }
                                        )
                                    )
                                    _logger.info(
                                        f"Lot {lot.lotNumber} found in Odoo for product {self.display_name} with difference qty {qty[self.id]['free_qty']} and AKS qty {lot.availableQuantity * pcb}"
                                    )
                                else:
                                    _logger.info(
                                        f"Lot {lot.lotNumber} found in Odoo for product {self.display_name} with no difference qty {qty[self.id]['free_qty']} and AKS qty {lot.availableQuantity * pcb}"
                                    )
                            else:
                                msg = f"Lot {lot.lotNumber} not found in Odoo for product {self.display_name}"
                                self.env["aks.sync.error"]._logme(model="product.product", operation="set_aks_fc_stockc", name=self.name, error_message=msg)
                                _logger.error(msg)
                                return False
                    else:
                        # no lot
                        res = self.env["stock.quant"].search([("product_id", "=", self.id), ("location_id", "=", aks_fc_location_id.id)])
                        if res:
                            res.action_clear_inventory_quantity()
                        if self.free_qty != fulfillments.resource.unitQuantity and self.tracking not in ["lot", "serial"]:
                            (
                                self.env["stock.quant"]
                                .with_context(inventory_mode=True)
                                .sudo()
                                .create(
                                    {
                                        "product_id": self.id,
                                        "location_id": aks_fc_location_id.id,
                                        "inventory_quantity": fulfillments.resource.unitQuantity,
                                    }
                                )
                            )
                            _logger.info(
                                f"Product {self.display_name} found in Odoo with difference qty {self.free_qty} and AKS qty {fulfillments.resource.unitQuantity}"
                            )
            except Exception as e:
                _logger.error(f"Failed to retrieve FC stock for product {self.display_name}: {e}")
                self.message_post(body=_(f"Error retrieving FC stock: {e}"))
                return False
            # inventory.action_apply_inventory()
            self.message_post(body=_(f"Inventory updated from AKS FC: Quantity: {fulfillments.resource.unitQuantity}"))
        return True
