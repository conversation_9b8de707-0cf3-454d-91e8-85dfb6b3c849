# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import logging

import requests

from odoo import api, models, fields, _
from odoo.exceptions import AccessDenied
from odoo.http import request


_logger = logging.getLogger(__name__)


class StockQuant(models.Model):
    _inherit = "stock.quant"
    is_aks_fc = fields.Boolean(related='location_id.is_aks_fc', store=True)
