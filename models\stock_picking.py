
# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import logging

from odoo import api, models, fields, _
from odoo.exceptions import UserError


_logger = logging.getLogger(__name__)


class StockPicking(models.Model):
    _inherit = "stock.picking"

    is_aks_fc = fields.Boolean(related='location_id.is_aks_fc', store=True)

    aks_id = fields.Char(string='AKS Internal Order ID', 
                         readonly=True,
                         help="The internal order ID in the Ankorstore system.",
                         copy=False)
    aks_master_order = fields.Char(string='AKS Master Order',
                                   readonly=True,
                                   copy=False)

    aks_external_order = fields.Char(string='AKS External Order',
                                     readonly=True,
                                     copy=False)
    customer_order_ref = fields.Char(string='Customer Order Reference')
    aks_latest_quote = fields.Char(string='AKS Delivery Latest Quote')
    aks_tracking_link = fields.Char(string='AKS Tracking Link')
    aks_sync_status = fields.Selection(
        string="AKS Synchronization Status",
        help="THe synchronization status of the delivery order to ANKORSTORE:\n",
        selection=[
                ('ankor_confirmed', "A new internal order is created for the brand"),
                ('awaiting_fulfillment', "Order is waiting for fulfillment"),
                ('cancelled', "Order is cancelled"),
                ('shipped', "Order is shipped from the warehouse"),
                ('received', "Order is arrived and completed"),
                ('rejected', "Order is rejected by the brand"),
                ('fulfillment_requested', "Fulfillment requested by the brand"),
                ('shipping_labels_generated', "Shipping labels generated"),
                ('reception_refused', "Reception refused by the retailer"),
                ('invoiced', "Order is invoiced"),
                ('brand_paid', "Brand paid"),
                ('brand_confirmed', "Brand confirmed the order"),       
                ('created', "A new external order is created from the brand"),
                ('arrived', "Order is arrived and completed")          
        ],
        readonly=True,
    )

    delivery_type = fields.Selection(related='carrier_id.delivery_type',
                                     store=False,
                                     string='Delivery Type')
    show_aks_confirm_custom_ship = fields.Boolean(compute='_compute_show_aks_confirm_custom_ship',
                                                  store=True, string='Show AKS Confirm Custom Ship')
    
    aks_confirm_custom_ship_done = fields.Boolean(string='AKS Confirm Custom Ship Done',default=False)    
    
    hide_aks_picking = fields.Boolean(compute='_compute_hide_aks_picking',
                                      string='Hide standard actions')

    @api.depends('is_aks_fc', 'aks_id', 'aks_master_order')
    def _compute_hide_aks_picking(self):
        for rec in self:
            if rec.is_aks_fc and (rec.aks_id or rec.aks_master_order) \
                  and rec.picking_type_id.code == 'outgoing':
                rec.hide_aks_picking = True
            else:
                rec.hide_aks_picking = False

    @api.depends('carrier_id', 'picking_type_id', 'aks_id', 'state', 'aks_confirm_custom_ship_done')
    def _compute_show_aks_confirm_custom_ship(self):
        for rec in self:
            if rec.carrier_id.delivery_type != 'aks' \
                    and rec.picking_type_id.code == 'outgoing' \
                    and rec.aks_id \
                    and not rec.is_aks_fc \
                    and rec.state == 'done'\
                    and not rec.aks_confirm_custom_ship_done:
                rec.show_aks_confirm_custom_ship = True
            else:
                rec.show_aks_confirm_custom_ship = False

    @api.model_create_multi
    def create(self, vals):
        """
        Overridden method from stock.picking.
        In addition to the original behavior, it checks if the picking
        is related to an AKS FC and if it is, it queues a job to create 
        the picking in AKS.
        """
        res = super(StockPicking, self).create(vals)    
        for record in res:
            if record.location_id.is_aks_fc:
                # self.create_aks_picking(record)
                # self.env["aks.queue.job"].queue_job_enqueue(
                # 'create_aks_picking', record, None)
                params = self.env["backend.aks"].search([('company_id', '=', self.company_id.id)])                
                record.carrier_id = params.aks_fc_shipping_method_id or self.env['delivery.carrier'].search([('delivery_type', '=', 'aks')], limit=1) or False
                followers = record.message_follower_ids
                partner_ids = followers.mapped('partner_id')
                record.message_unsubscribe(partner_ids.ids)
                record.message_subscribe(self.env["backend.aks"].get_followers())
        return res

    def button_aks_confirm_custom_ship(self):
        for rec in self:
            if rec.carrier_id.delivery_type != 'aks' \
               and rec.picking_type_id.code == 'outgoing' \
               and rec.aks_id \
               and not rec.is_aks_fc:
                # send custom shipping request to Ankorstore API
                # normaly , we ve got here the tracking number, parcels, provider 
                # endpoint update packages
                # https://www.public.ankorstore-sandbox.com/api/v1/orders/{order}/ship/custom
                carrier_tracking_url = rec.carrier_id.get_tracking_link(rec) if rec.carrier_id and rec.carrier_tracking_ref else False
                packages = rec.package_ids            
                if carrier_tracking_url and packages:
                    parcels = []
                    for package in packages:
                        if package.package_type_id:
                            parcels.append({
                                "length": int(package.package_type_id.packaging_length/10),
                                "width": int(package.package_type_id.width/10),
                                "height": int(package.package_type_id.height/10),
                                "distanceUnit": "cm",
                                "weight": package.weight*1000,
                                "massUnit": "g",
                            })     
                    if parcels:
                        payload = {
                                "shipping": {"parcels": parcels}
                            }
                        backend = self.env["backend.aks"].get_instance()
                        session = backend.get_session()
                        url = self.env["backend.aks"].get_base_url()
                        path = url + f"/api/v1/orders/{rec.aks_id}/ship/custom"
                        quotes = session.http_request('POST', path, payload)
                        data = quotes[1]['data']
                        quote = data["attributes"]["shippingOverview"]["latestQuote"]["id"]
                        path = url + f"/api/v1/shipping-quotes/{quote}/confirm"
                        if carrier_tracking_url:
                            payload = {"tracking": {
                                        "trackingLink": carrier_tracking_url
                                    }}
                        else:
                            payload = {}
                        try:
                            ret = session.http_request('POST', path, payload)
                            rec.aks_confirm_custom_ship_done = True
                            rec.message_post(body=f"Custom Shipping information sent to Ankorstore API. Tracking URL: {carrier_tracking_url}")
                        except Exception as e:
                            error_message = f"Error while sending shipping request to Ankorstore API: {e}"
                            self.env['aks.sync.error']._logme(
                                model='stock.picking',
                                operation='button_aks_confirm_custom_ship',
                                name=rec.name,
                                error_message=error_message)
                            raise UserError(_(f"Error while sending shipping request to Ankorstore API: {e}"))
                        # give tracking
                        # https://www.public.ankorstore-sandbox.com/api/v1/shipping-quotes/{quote}/confirm
                else:
                    return {
                        'name': _('Enter Shipping Information'),
                        'type': 'ir.actions.act_window',
                        'view_mode': 'form',
                        'res_model': 'shipping.info.wizard',
                        'target': 'new',
                        'context': {'active_id': self.id},
                    }

    def _prepare_aks_data(self):
        """
        Prepare the data to be sent to AKS.
                    External order :
                    {
            "shippingAddress": {
                "country": "string",
                "postalCode": "string",
                "city": "string",
                "street": "string",
                "company": "string",
                "firstName": "string",
                "lastName": "string",
                "phone": "string",
                "email": "<EMAIL>"
            },
            "items": [
                {
                "fulfillableId": "611a6658-a5d5-475f-8280-4b693104739b",
                "quantity": 0
                }
            ],
            "masterOrderUuid": "8f6deb2e-3f81-488c-8887-508b733f0a5b",
            "customReference": "string"
            }
        """
        self.ensure_one()

        items = self._prepare_aks_lines_data()
        is_company = False
        if self.partner_id.parent_id:
            is_company = self.partner_id.parent_id.is_company
        else:
            is_company = self.partner_id.is_company
        if is_company:
            recipientype = 'business'
        else:
            recipientype = 'consumer'
        company = self.partner_id.parent_id.name if self.partner_id.parent_id else self.partner_id.name
        data = {
                "shippingAddress": {
                    "country": self.partner_id.country_id.code,
                    "postalCode": self.partner_id.zip,
                    "city": self.partner_id.city[0:254],
                    "street": self.partner_id.street[0:59],
                    "company": company[0:69],
                    "firstName": self.partner_id.name[0:63],
                    "lastName": self.partner_id.name[0:63],
                    "phone": self.partner_id.mobile if self.partner_id.mobile else self.partner_id.phone,
                    "email": self.partner_id.email
                },
                "items": items,
                "masterOrderUuid": self.aks_master_order,
                "customReference": self.name,
                "recipientType": recipientype
                }
        return data

    def _prepare_aks_lines_data(self):
        """
        Prepare the data to be sent to AKS.
        """
        self.ensure_one()
        items = []
        for line in self.move_ids_without_package:
            items.append({
                "fulfillableId": line.fulfillableid,
                "quantity": int(line.product_uom_qty)
            })
        return items

    def get_shipping_overview(self):
        """_summary_ = 'Get shipping overview from Ankorstore API'
        retrieve labels and tracking number
        """
        self.ensure_one()
        if self.carrier_id.delivery_type == 'aks':
            self.carrier_id.aks_get_shipping_overview(self)

    def get_last_status(self):
        """
        EXTERNAL ORDER
        Get the last status of the picking.
        assigned
        """
        self.ensure_one()
        backend = self.env["backend.aks"].get_instance()
        include = backend.inclusion('externalOrder', 'shipment')
        master_order = self.aks_master_order
        session = backend.get_session()
        res = session.get(f"master-orders/{master_order}", include)
        status = res.resource.externalOrder.status
        shipment = res.resource.shipment
        self.aks_sync_status = status
        self.aks_tracking_link = shipment.trackingUrl if shipment.trackingUrl else False
        self.carrier_tracking_ref = shipment.trackingNumber if shipment.trackingNumber else False
        # shipping overview 
        if self.aks_sync_status in ['created', 'awaiting_fulfillment']:
            self.action_assign()
            self.message_post(body="Awaiting fulfillment. The order has been assigned to a warehouse.")
        if self.aks_sync_status == 'shipped':
            if self.state != 'assigned':
                self.action_assign()
            fullfilments_ids = self.get_fullfilment_ids(master_order)
            # for fullfilment_id in fullfilments_ids:
            if fullfilments_ids:
                move_lines = self.get_fulfillment_status(fullfilments_ids[0])
            self.with_context(cancel_backorder=True, skip_sms=True).sudo().button_validate()
            self.message_post(body="The order has been shipped.")
        elif self.aks_sync_status in ['arrived', 'received']:
            if self.state != 'done':
                if self.state != 'assigned':
                    self.action_assign()
                fullfilments_ids = self.get_fullfilment_ids(master_order)
                # for fullfilment_id in fullfilments_ids:
                if fullfilments_ids:
                    move_lines = self.get_fulfillment_status(fullfilments_ids[0])
                self.with_context(cancel_backorder=True, skip_sms=True).sudo().button_validate()
            self.message_post(body="The order has been received by the customer.")
        elif self.aks_sync_status == 'cancelled':
            self.action_cancel()
        return True

    def create_aks_picking(self):
        """
        Export the picking to AKS.
        using :
        """
        self.ensure_one()
        backend = self.env["backend.aks"].get_instance()
        url = self.env["backend.aks"].get_base_url()
        path = url + "/api/v1/external-orders/non-ankorstore-fulfillment-orders"
        session = backend.get_session()
        dict_to_send = self._prepare_aks_data()
        try:
            picking = session.http_request('POST', path, dict_to_send)
            _logger.info(f"Picking created in AKS : {self.name}")
            self.aks_sync_status = 'created'
        except Exception as e:
            error_message = f"Error while creating picking in AKS : {e}" + f" {dict_to_send}"    
            self.env['aks.sync.error']._logme(
                model='stock.picking',
                operation='create_aks_picking',
                name=self.name,
                error_message=error_message)
            _logger.warning(error_message)
            return False
        return True

    def get_fullfilment_ids(self, master_order):
        """
        Get the fulfillment ids of the picking.
        https://www.public.ankorstore-sandbox.com/api/v1/master-orders/{orderId}/relationships/fulfillment-orders
        """
        if not master_order:
            return False
        self.ensure_one()
        backend = self.env["backend.aks"].get_instance()
        path = f"master-orders/{master_order}/relationships/fulfillment-orders"
        session = backend.get_session()
        res = session.get(path)
        fulfillment_ids = res.resources if res.resources else False
        return fulfillment_ids

    def get_fulfillment_status(self, fulfillment_id):
        """
        Get the fulfillment status of the picking.
        https://www.public.ankorstore-sandbox.com/api/v1/fulfillment/orders/{id}
        """
        self.ensure_one()
        backend = self.env["backend.aks"].get_instance()
        path = f"fulfillment/orders/{fulfillment_id.id}"
        session = backend.get_session()
        res = session.get(path)
        # need to return the move lines
        # retrieve products and lots
        # reset move line ids
        move_line_vals = []
        after_state = {}
        for line in res.resource.shippedItems:
            # [{'fulfillableId': '1eec3fa5-bb09-64ce-923c-d2a2dbe54a33', 'batchQuantity': 40, 'unitQuantity': 1000, 'lotNumber': None}]
            # return the product and the lot : [product, lot, quantity]
            product_id = self.env['product.product'].search([('aks_fulfillable_id', '=', line['fulfillableId'])], limit=1)
            if not product_id:
                _logger.warning(f"Product not found in Odoo for fulfillableId {line['fulfillableId']}")
                return False
            if line['lotNumber']:
                lot_id = self.env['stock.lot'].search([('name', '=', line['lotNumber']), ('product_id', '=', product_id.id)],limit=1)
            else:
                lot_id = False
            if product_id.tracking == 'none' and lot_id:
                lot_id = False
            if product_id.tracking == 'lot' and not lot_id:
                msg = f"Lot not found in Odoo Product {product_id.name} for lotNumber {line['lotNumber']}"
                self.env['aks.sync.error']._logme(model='stock.picking',
                                                  operation='get_fulfillment_status',
                                                  name=self.name,
                                                  error_message=msg)
                self.message_post(body=msg)
                _logger.warning(msg)
                return False
            move_line_vals.append({
                'product_id': product_id.id,
                'quantity': line['unitQuantity'],
                'lot_id': lot_id.id if lot_id else False,
                'location_id': self.location_id.id,
                'location_dest_id': self.location_dest_id.id,
                'product_uom_id': product_id.uom_id.id,
                'picking_id': self.id,
            })

            key = (product_id.id, lot_id.id if lot_id else False)
            after_state[key] = {
                                'product_name': product_id.name,
                                'lot_name': lot_id.name if lot_id else 'No Lot',
                                'quantity': line['unitQuantity']
                            }
        if not move_line_vals:
            return False

        before_move_line_ids = self.move_line_ids
        before_state = {}
        for line in before_move_line_ids:
            key = (line.product_id.id, line.lot_id.id if line.lot_id else False)
            before_state[key] = {
                'product_name': line.product_id.name,
                'lot_name': line.lot_id.name if line.lot_id else 'No Lot',
                'quantity': line.quantity
            }
        
        before_move_line_ids.sudo().unlink()

        StockMoveLine = self.env["stock.move.line"]
        StockMoveLine.sudo().create(move_line_vals)

        # self.compare_and_post_exceptions(before_state, after_state)
        return True

    def compare_and_post_exceptions(self, before_state, after_state):
        """
        Compare before and after states for lots and quantities, and post exceptions if any.
        """
        exceptions = {}
        all_keys = set(before_state.keys()).union(set(after_state.keys()))
        for key in all_keys:
            before = before_state.get(key, {'quantity': 0})
            after = after_state.get(key, {'quantity': 0})
            product_id, lot_id = key
            if before['quantity'] != after['quantity']:
                if product_id not in exceptions:
                    exceptions[product_id] = []
                if before['quantity'] == 0 and after['quantity'] != 0:
                    exceptions[product_id].append(f"New lot added: {after['lot_name']} with quantity {after['quantity']}")
                elif before['quantity'] != 0 and after['quantity'] == 0:
                    exceptions[product_id].append(f"Lot {before['lot_name']} removed, was with quantity {before['quantity']}")
                else:
                    exceptions[product_id].append(f"Lot {before['lot_name']}: Quantity changed from {before['quantity']} to {after['quantity']}")

        for product_id, exception_list in exceptions.items():
            product_name = before_state.get((product_id, None), after_state.get((product_id, None)))['product_name']
            if exception_list:
                exception_message = f"Exceptions detected for product {product_name}:\n" + "\n".join(exception_list)
                self.message_post(body=exception_message)