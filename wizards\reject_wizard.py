
from odoo import _, api, models, fields


class AksRejectWizard(models.TransientModel):
    """Reject with reaseon Selection"""

    _name = "aks.reject.wizard"
    _description = "Reject with reaseon Selection"

    aks_reject_reason = fields.Selection([
        ('BRAND_ALREADY_HAS_CUSTOMER_IN_THE_AREA', 'Brand already has customer in the area'),
        ('BRAND_CANNOT_DELIVER_TO_THE_AREA', 'Brand cannot deliver to the area'),
        ('BRAND_HAS_EXCLUSIVE_DISTRIBUTOR_IN_THE_REGION', 'Brand has exclusive distributor in the region'),
        ('BUYER_NOT_A_RETAILER', 'Buyer not a retailer'),
        ('ORDER_ITEMS_PRICES_INCORRECT', 'Order items prices incorrect'),
        ('PAYMENT_ISSUES_WITH_RETAILER', 'Payment issues with retailer'),
        ('PREPARATION_TIME_TOO_HIGH', 'Preparation time too high'),
        ('PRODUCT_OUT_OF_STOCK', 'Product out of stock'),
        ('PURCHASE_NOT_FOR_RESALE', 'Purchase not for resale'),
        ('RETAILER_AGREED_TO_DO_CHANGES_TO_ORDER', 'Retailer agreed to do changes to order'),
        ('RETAILER_NOT_GOOD_FIT_FOR_BRAND', 'Retailer not good fit for brand'),
        ('RETAILER_VAT_NUMBER_MISSING', 'Retailer VAT number missing'),
        ('OTHER', 'Other')],
        string="AKS Reject Reason",
        copy=False)

    aks_reject_details = fields.Char(string="Reject Details", size=1000, copy=False)

    sale_order_id = fields.Many2one("sale.order", string="Sale Order", ondelete="cascade", readonly=True)

    def default_get(self, fields):
        defaults = super(AksRejectWizard, self).default_get(fields)
        defaults["sale_order_id"] = self._context.get("active_id")
        return defaults

    def action_reject_order(self):
        self.ensure_one()
        self.sale_order_id.aks_reject_reason = self.aks_reject_reason
        self.sale_order_id.aks_reject_details = self.aks_reject_details
        self.sale_order_id.action_reject()
        return {
            'name': _('Reject Order'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }
