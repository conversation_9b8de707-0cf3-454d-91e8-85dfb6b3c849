# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
import json
import logging
from odoo import api, models, fields


_logger = logging.getLogger(__name__)


class AksQueueJob(models.Model):
    """
    Dedicating a model to queue jobs to avoid conflicts with other modules
    (OCA or others). Keep the queue job simple and generic on standard odoo
    planned tasks.
    """
    _name = 'aks.queue.job'
    _description = 'Queue Job'

    name = fields.Char(required=True, index=True)
    method_name = fields.Char(required=True)
    error_message = fields.Text('Error Message')
    args = fields.Text()
    kwargs = fields.Text()
    state = fields.Selection([
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('done', 'Done'),
        ('failed', 'Failed')
    ], default='pending')
    retry = fields.Boolean(default=False, compute='_compute_retry')

    @api.depends('state')
    def _compute_retry(self):
        """Compute retry based on job state and error type."""
        for record in self:
            if record.state == 'failed':
                # TODO: eval the error message to see if it is a retryable error
                record.retry = True
            else:
                record.retry = False

    @api.model
    def get_pending_jobs(self, limit=None):
        """Fetch pending jobs with an optional limit."""
        jobs = self.search([('state', '=', 'pending')], limit=limit)
        return jobs

    @api.model
    def process_pending_jobs(self):
        """Process all pending jobs."""
        pending_jobs = self.get_pending_jobs()
        for job in pending_jobs:
            job.state = 'processing'
            try:
                method = getattr(self.env[job.name], job.method_name)
                args = eval(job.args)
                kwargs = eval(job.kwargs)
                method(*args, **kwargs)
                job.state = 'done'
            except Exception as e:
                job.state = 'failed'  # FIXME : add a retry mechanism, job keeps in pending state 
                error_message = f"Error while processing job {job.name} : {e}"
                _logger.warning(error_message)
                self.env['aks.sync.error']._logme(
                    model='aks.queue.job',
                    operation='process_pending_jobs',
                    name=job.name,
                    error_message=error_message)

    @api.model
    def queue_job_enqueue(self, method_name, *args, **kwargs):
        """Enqueue a new job."""
        job = self.create({
            'name': 'wc.sync',
            'method_name': method_name,
            'args': repr(args),
            'kwargs': repr(kwargs),
            'state': 'pending',
        })
        return job.id
