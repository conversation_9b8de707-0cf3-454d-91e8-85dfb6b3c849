from odoo import _, api, models, fields


class AksCreateTestOrder(models.TransientModel):
    """Reject with reaseon Selection"""

    _name = "aks.test.order.wizard"
    _description = "Create Test Order"

    retailer = fields.Char(string="Retailer ID", default="1ec4613a-6e52-6200-8d7e-82329cff240e")
    # partner_id = fields.Many2one("res.partner", string="Partner")
    line_ids = fields.One2many("aks.test.order.line.wizard", "wizard_id", string="Order Lines")

    def action_create_test_order(self):
        self.ensure_one()
        path = "/api/testing/orders/create"
        product_variants = []
        for line in self.line_ids:
            product_variants.append({"id": line.product_id.aks_id, "quantity": int(line.quantity)})
        data = {
                    "retailerId": self.retailer,
                    "productVariants": product_variants
                }

        backend = self.env["backend.aks"].get_instance()
        url = self.env["backend.aks"].get_base_url()
        path = url + path        
        session = backend.get_session()
        ret = session.http_request('POST', path, data)

        if ret[0] == 200:
            return {
                'name': _('Test Order Created'),
                'type': 'ir.actions.act_window',
                'res_model': 'aks.test.order.wizard',
                'view_mode': 'form',
                'res_id': self.id,
                'target': 'new',
            }


class AksTestOrderLine(models.TransientModel):

    _name = "aks.test.order.line.wizard"
    _description = "Create Test Order Line"

    wizard_id = fields.Many2one("aks.test.order.wizard", string="Wizard", readonly=True, ondelete="cascade")
    product_id = fields.Many2one("product.product", string="Product")
    quantity = fields.Float(string="Quantity")
