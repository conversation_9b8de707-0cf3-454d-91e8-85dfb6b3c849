# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
import logging

from odoo import _, http
from odoo.http import request, Response
from urllib.parse import unquote_plus
import json


_logger = logging.getLogger(__name__)


class AksWebhookController(http.Controller):
    @http.route("/connector_aks/webhooks/<int:company_id>", type="json",methods=['POST'], auth="none", csrf=False)
    def webhook_(self, company_id=None, **post):
        # main controller for handling AKS webhooks
        posted_data = request.httprequest.get_data()
        data = json.loads(posted_data)
        event_type = data["meta"]["event"]["type"]
        order_id = data["data"]["id"]

        # status = data["data"]["attributes"]["status"]
        status = data.get("data", {}).get("attributes", {}).get("status", False)
        kw = {
            "type": type,
            "id": id,
        }
        env = request.env
        backend = env["backend.aks"].sudo().search([('company_id', '=', company_id)])  # get the instance of the AKS backend company
        try:
            backend.with_context(default_company_id=company_id)._handle_webhook(event_type, order_id, status)
            return {"status": "200"}
        except Exception as e:
            _logger.exception('Error processing AKS webhook: %s', e)
            return {
                'status': 'error',
                'message': str(e)
                }
