from odoo import _, api, fields, models
from odoo.exceptions import ValidationError


class ChooseDeliveryCarrier(models.TransientModel):
    _inherit = 'choose.delivery.carrier'

    line_ids = fields.One2many(
        "aks.quote.lookup", "wizard_id", string="Results", readonly=True
    )
    service_commercial_name = fields.Char(string="Service Commercial Name")
    quote_uuid = fields.Char(string="Quote UUID")
    package_id = fields.Many2one("stock.package.type", string="Default Package Type")
    is_hazardous_goods = fields.Boolean(string="Hazardous Goods?", default=False)
    package_line_ids = fields.One2many("aks.package.line", "wizard_id", string="Packages")
    calculate_parcel_method = fields.Selection([
        ('manual', 'Manual'),
        ('automatic', 'Automatic')
    ], string="Calculate Parcel Method", default="automatic")

    @api.model
    def default_get(self, fields):
        res = super(ChooseDeliveryCarrier, self).default_get(fields)
        if self.carrier_id.delivery_type == 'aks':
            res["package_id"] = self.carrier_id.aks_default_package_type_id.id        
        return res

    def get_lines_quote(self):
        self.ensure_one()
        self.line_ids.unlink()
        if not self.order_id.aks_id:
            raise ValidationError(_("You can only get quotes for Ankorstore orders"))
        if self.order_id.aks_mode == "FFA":
            raise ValidationError(_("You can only get quotes if you ship the order yourself"))
        if self.order_id.state == "draft":
            raise ValidationError(_("You can only get quotes for ankorstrore confirmed orders"))
        # Get request
        # V15 : not exist 
        #  packages = self.carrier_id._get_packages_from_order(self.order_id, self.carrier_id.aks_default_package_type_id)
        packages = []
        total_weight = self.order_id._get_estimated_weight()
        if not self.package_id:
            self.package_id = self.carrier_id.aks_default_package_type_id
        if total_weight == 0:
            raise ValidationError(_("No weight found for the order"))
        max_weight = self.package_id.max_weight
        if self.calculate_parcel_method == 'automatic':
            if max_weight == 0:
                raise ValidationError(_("No max weight found for the package type"))
            if max_weight and total_weight > max_weight:
                total_package = int(total_weight / max_weight)
                last_package_weight = total_weight % max_weight

                for seq in range(total_package):
                    packages.append(Package(self.package_id, max_weight))
                if last_package_weight:
                    packages.append(Package(self.package_id, last_package_weight))
            else:
                packages.append(Package(self.package_id, total_weight))

        for line in self.package_line_ids:
            for seq in range(line.parcel_number):
                packages.append(Package(line.package_id, line.parcel_weight))

        dict_to_send = self._prepare_dict_quote(packages)
        # Fill new company lines
        aks_order = self.order_id.aks_id
        backend = self.env["backend.aks"].get_instance()
        session = backend.get_session()
        url = self.env["backend.aks"].get_base_url()
        path = url + f"/api/v1/orders/{aks_order}/shipping-quotes"
        try:
            quotes = session.http_request('POST', path, dict_to_send)
        except Exception as e:
            raise ValidationError(_("Error while getting quotes from Ankorstore API: %s" % e))
        decode = quotes[1]["data"]
        for line in decode:
            vals = self._prepare_quote_from_data(line)
            self.line_ids.create(vals)
        return {
            'name': _('Add a shipping method'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'choose.delivery.carrier',
            'res_id': self.id,
            'target': 'new',
        }

    def _prepare_quote_from_data(self, data):
        return {
            "wizard_id": self.id,
            "currency": data.get("shippingCost").get("currency"),
            # "collection_method": data.get("collectionMethod"),
            "quote_uuid": data.get("quoteUuid"),
            "carrier_code": data.get("carrierCode"),
            "service_code": data.get("serviceCode"),
            "service_commercial_name": data.get("serviceCommercialName"),
            "amount": data.get("shippingCost").get("amount")/100,
        }

    def _prepare_dict_quote(self, packages):
        """ Prepare dict to send to Ankorstore API
        """
        vals = []
        for package in packages:
            vals.append({
                "height": int(package.dimension["height"]/10),
                "length": int(package.dimension["length"]/10),
                "width": int(package.dimension["width"]/10),
                "kg": package.weight,
            })

        payload = {
                    "parcels": vals,
                    "no_dangerous_product": not self.is_hazardous_goods
                }
        return payload

    def button_confirm(self):
        if self.carrier_id.delivery_type == 'aks':
            if not self.quote_uuid:
                raise ValidationError(_("You must select a shipping quote to confirm the order"))
        self.order_id.set_delivery_line(self.carrier_id, self.delivery_price)
        self.order_id.write({
                'recompute_delivery_price': False,
                'delivery_message': self.delivery_message,
                'aks_latest_quote': self.quote_uuid,
            })
        if self.quote_uuid:
            self.order_id.action_confirm_delivery_quote()


class Package():
    def __init__(self, package, weight):
        self.weight = weight
        self.dimension = {'length': package.packaging_length, 'width': package.width, 'height': package.height}