# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.
from requests_oauthlib import OAuth2Session
from oauthlib.oauth2 import BackendApplicationClient
from oauthlib.oauth2.rfc6749.errors import OAuth2Error
import requests
from cachetools import TTLCache
# respect json:api requirements
from .jsonapi_client import Session, Filter, Inclusion, Modifier
import logging
_logger = logging.getLogger(__name__)


class AksClient:
    """
    Ankorstore API client wrapper.
    Prepare and make requests to the Ankorstore API in ORM approach.
    The client is based on the OAuth2 protocol.
    Using json:api client to make requests.
    """

    def __init__(self, base_url, client_id, client_secret, scope='*'):
        """
        Initialize the AksClient.

        Args:
            base_url (str): The base URL for the Ankorstore API.
            client_id (str): The client ID for OAuth2 authentication.
            client_secret (str): The client secret for OAuth2 authentication.
            scope (str, optional): The scope for OAuth2 authentication. Defaults to '*'.
        """
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        self.client_id = client_id
        self.client_secret = client_secret
        self.scope = scope
        self.token_cache = TTLCache(maxsize=1, ttl=3600)  # Cache
        self.access_token = None

    def create_oauth_client(self):
        """
        Create an OAuth2 client and fetch the access token.

        Returns:
            OAuth2Session: The created OAuth2 client.

        Raises:
            Exception: If there is an error while creating the OAuth2 client.
        """

        token_url = f"{self.base_url}/oauth/token"
        # Create an OAuth2 client session
        client = OAuth2Session(
            client=BackendApplicationClient(client_id=self.client_id,))
        try:
            # Fetch the access token
            client.fetch_token(
                token_url=token_url,
                client_id=self.client_id,
                client_secret=self.client_secret,
                scope=self.scope,
            )
            # Store the access token
            self.access_token = client.token['access_token']
            # Cache the access token
            self.token_cache['access_token'] = self.access_token
            return client
        except Exception as e:
            # Log and raise any exceptions
            error_message = f"Error while creating AKS OAuth2 client : {e}"
            _logger.warning(error_message)
            raise

    def get_cached_token(self):
        """
        Get the cached access token.

        Returns:
            str: The cached access token, or None if no token is cached.
        """
        return self.token_cache.get('access_token')

    def get_session(self):
        """
        Get a json:api session.

        This method first checks if there is a cached access token.
        If there is, it uses that token.
        If there isn't, it creates a new OAuth2 client
        and fetches a new access token.

        Returns:
            Session: A json:api session with the access token set in the Authorization header.

        Raises:
            Exception: If there is an error while creating the json:api session.
   
        """
        try:
            cached_token = self.get_cached_token()
            if cached_token:
                self.access_token = cached_token
            else:
                self.create_oauth_client()
            return Session(self.api_url, request_kwargs=dict(headers={'Authorization': f"Bearer {self.access_token}"}))
        except Exception as e:
            error_message = f"Error while creating AKS json:api session : {e}"
            _logger.warning(error_message)
            raise

    def inclusion(self, *args):
        """
        Get a json:api inclusion.

        Args:
            *args: Variable length argument list.

        Returns:
            Inclusion: A json:api inclusion.
        """
        return Inclusion(*args)

    def filter(self):
        """
        Get a json:api filter.

        Args:
            *args: Variable length argument list.

        Returns:
            Filter: A json:api filter.
        """
        return Filter()

    def modifier(self, *args):
        """
        Get a json:api modifier.

        Args:
            *args: Variable length argument list.

        Returns:
            Modifier: A json:api modifier.
        """
        return Modifier(*args)

    def make_api_request(self, method='GET', path='', data=None, params=None, headers=None,  force_url=None):
        """
        Make a simple API request without using the jsonapi client.

        Args:
            method (str, optional): The HTTP method for the request. Defaults to 'GET'.
            path (str, optional): The path for the request. Defaults to ''.
            data (dict, optional): The data to send in the request. Defaults to None.
            params (dict, optional): The query parameters for the request. Defaults to None.
            headers (dict, optional): The headers for the request. Defaults to None.

        Returns:
            Response: The response from the API.

        Raises:
            OAuth2Error: If there is an OAuth2 error while making the request.
        """

        # Get the cached access token
        cached_token = self.get_cached_token()
        if cached_token:
            self.access_token = cached_token
        else:
            # If there is no cached token, create a new OAuth2 client and fetch a new token
            self.create_oauth_client()

        # Prepare the full URL for the request headers = {'Content-Type':'application/vnd.api+json', 'Accept':'application/vnd.api+json'}
        full_url = f"{self.base_url}/{path}"
        headers = headers or {}
        headers['Accept'] = "application/vnd.api+json"
        headers["x-ankorstore-app"] = "odoo"
        headers['Authorization'] = f"Bearer {self.access_token}"

        try:
            # Make the request
            if force_url:
                full_url = force_url
            response = requests.request(
                method, full_url, data=data, params=params, headers=headers)
            response.raise_for_status()
            return response
        except OAuth2Error as e:
            print(f"OAuth2Error: {str(e)}")
            if e.error == 'invalid_token':
                # If token is invalid, refresh it and make request again
                self.refresh_token()
                headers['Accept'] = "application/vnd.api+json"
                headers["x-ankorstore-app"] = "odoo"
                headers['Authorization'] = f"Bearer {self.access_token}"
                response = requests.request(
                    method, full_url, data=data, params=params, headers=headers)
                response.raise_for_status()
                return response
            else:
                raise  # If error not due to invalid token, raise error

    def refresh_token(self):
        """
        Refresh the access token.
        Creates a new OAuth2 client and fetches a new access token.
        """
        try:
            self.create_oauth_client()
            print("Token refreshed successfully.")
        except Exception as e:
            print(f"Token refresh failed: {str(e)}")

    def post_test_order(self, data):
        """
        Create a test internal order.
        """
        endpoint = "api/testing/orders/create"
        return self.make_api_request(method='POST', path=endpoint, data=data)

    def get_product_variant(self, variant_id=None):
        """
        Get a product variant.
        """
        response = self.make_api_request(method='GET', path=f'api/v1/product-variants/{variant_id}')
        if response.status_code == 200:
            return response.json()
        else:
            _logger.error(f"Failed to retrive variant: {variant_id}")
            return None

    # WEBHOOK methods
    def list_ask_apps(self):
        """
        Fetch a list of applications registered under the Ankorstore account.
        """
        response = self.make_api_request('GET', path='api/v1/applications')
        if response.status_code == 200:
            return response.json()
        else:
            _logger.error(f"Failed to list applications: {response.text}")
            return None

    def subscribe_to_webhooks(self, app_id, webhook_url, events):
        """
        Subscribe to a list of webhook events for the given application.
        """
        payload = {
            "data": {
                "type": "webhook-subscriptions",
                "attributes": {
                    "url": webhook_url,
                    "events": events
                },
                "relationships": {
                    "application": {
                        "data": {
                            "type": "applications",
                            "id": app_id
                        }
                    }
                }
            }
        }

        session = self.get_session()
        response_status, response_data, _ = session.http_request(
            'POST', f"{self.api_url}/webhook-subscriptions", payload)
        if response_status in [200, 201]:
            _logger.info("Webhook subscriptions created successfully.")
            return response_data
        else:
            error_message = (f"Failed to create webhook subscription: {response_status} {response_data}")
            _logger.error(error_message)
            raise Exception(error_message)

    def unsubscribe_from_webhook(self, sub_id):
        """
        Unsubscribe from a webhook given its subscription ID.
        """
        session = self.get_session()
        response_status, response_data, _ = session.http_request(
            'DELETE', f"{self.api_url}/webhook-subscriptions/{sub_id}", send_json=False)
        if response_status in [200, 204]:
            _logger.info("Successfully unsubscribed from webhook.")
            return True
        else:
            error_message = f"Failed to unsubscribe from webhook: {response_status} {response_data}"
            _logger.error(error_message)
            return False
