# Part of Connector ANKORSTORE.
# See LICENSE file for full copyright and licensing details.

import logging

from odoo import fields, models

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    _inherit = "stock.move"
    """
    Overridden method from stock.move.
    It checks if the product is exported to AKS.
    If it is, it queues a job to update the product stock in AKS.
    """

    is_aks_fc = fields.Boolean(related="location_id.is_aks_fc", store=True)
    fulfillableid = fields.Char(string="Fulfillable ID", related="sale_line_id.fulfillableid", store=True, copy=False)

    def _get_new_picking_values(self):
        vals = super()._get_new_picking_values()
        aks_master_order_uuid = self.group_id.sale_id.aks_external_master_order
        vals["aks_master_order"] = aks_master_order_uuid
        vals["customer_order_ref"] = self.group_id.sale_id.reference
        vals["aks_id"] = self.group_id.sale_id.aks_id
        vals["aks_latest_quote"] = self.group_id.sale_id.aks_latest_quote

        return vals

    def _prepare_procurement_values(self):
        res = super()._prepare_procurement_values()
        res["fulfillableid"] = self.sale_line_id.fulfillableid
        return res
