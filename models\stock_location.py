# -*- coding: utf-8 -*-
# Part of Connector ANKORSTORE. See LICENSE file for full copyright and licensing details.

import logging
from odoo import api, models, fields, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class StockLocation(models.Model):
    _inherit = "stock.location"

    is_aks_fc = fields.Boolean(
        string="Is AKS Location Fulfillment Center",
        related='location_id.warehouse_id.is_aks_fc',
        store=True,
    )
