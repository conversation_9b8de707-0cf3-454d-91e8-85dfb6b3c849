<!-- backend_aks_views.xml -->
<odoo>

    <!-- Form View -->
    <record id="view_backend_aks_form" model="ir.ui.view">
        <field name="name">backend.aks.form</field>
        <field name="model">backend.aks</field>
        <field name="arch" type="xml">
            <form string="Setup">
                <field name="aks_integration_status" invisible="1"/>
                <field name="aks_active_fc" invisible="1"/>
                <field name="environment" invisible="1"/>
                <field name="active" invisible="1"/>
                <field name="connected" invisible="1"/>
                <header>
                    <button name="action_activate_connection" string="Activate connection" type="object" class="btn btn-sm oe_highlight" invisible="aks_integration_status" groups="base.group_system"/>
                    <button name="action_deactivate_connection" string="Deactivate connection" type="object" class="btn btn-sm oe_highlight" invisible="not aks_integration_status" groups="base.group_system"/>
                    <button name="action_list_catalog" string="Reconcile catalog" type="object" class="btn btn-sm oe_highlight" groups="base.group_system"/>
                    <button name="action_trigger_sync_aks_fc_stock" string="Retrieve FC inventory from Ankorstore" type="object" class="btn btn-sm oe_highlight" invisible="not aks_active_fc" groups="base.group_system"/>
                    <button name="action_trigger_sync_aks_stock" string="Export inventory to Ankorstore" type="object" class="btn btn-sm oe_highlight" groups="base.group_system"/>
                    <button name="action_view_create_order" string="Create Internal Order as a retailer" type="object" class="btn btn-sm oe_highlight" invisible="environment != 'dev'" groups="base.group_system"/>

                    <!-- <button name="action_whoiam" string="Test Connection" type="object" class="oe_highlight"/> -->
                    <!-- <button name="action_trigger_list_aks_orders" string="Sync AKS Orders" type="object" class="oe_highlight"/>-->
                    <!-- <button name="action_subscribe_to_all_webhooks" string="Create All Webhook Subscriptions" type="object" class="oe_highlight"/> -->
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Unconnected" bg_color="bg-danger" invisible="connected"/>
                    <widget name="web_ribbon" title="Connected" bg_color="bg-success" invisible="not connected"/>

                    <group name="Setup" string="Credentials">
                        <group>
                            <field name="name" help="Define a name to recognize this Ankorstore connection" readonly="True"/>
                            <field name="company_id" help="Pre-filled field with your Odoo information"/>
                            <field name="environment"/>
                            <!-- <field name="base_url_prod" invisible="environment != 'prod'"/>
                            <field name="base_url_dev" invisible="environment != 'dev'" force_save="1" readonly="True"/>-->
                            <field name="client_id" password="True" help="Get your unique Client ID from your Ankorstore account to authenticate the connection between Ankorstore and Odoo"/>
                            <field name="client_secret" string="Client secret" password="True" help="Get your unique Client secret from your Ankorstore account to authenticate the connection between Ankorstore and Odoo"/>
                            <field name="aks_integration_status" invisible="True"/>
                            <field name="use_webhooks"  widget="boolean_toggle" invisible="1"/>
                            <field name="odoo_webhook_base_url" string="Webhook Base URL" invisible="1" readonly="True"/>
                            <field name="aks_app_name" help="/!\ Important: You must use the same name as the application name used when created in Ankorstore. Otherwise, systems will not be able to communicate"/>
                            <field name="aks_app_id" readonly="True" invisible="1"/>
                            <field name="active" invisible="1" />
                            <field name="log_folllower_ids" string="Log followers" widget="many2many_tags" help="Define which people in your organization should receive potential backend errors"/>
                            <!-- <field name="help_url_1" string="Learn more" help="Click on the link to know how to get your Ankorstore Client ID and Client secret in Ankorstore" readonly="True"> or <button that open url> -->
                        </group>
                        <group name="help" string="Help">
                            <button name="action_help_url_2" type="object" string="Learn more" help="Click on the link to know how to get your Ankorstore Client ID and Client secret in Ankorstore"/>
                        </group>
                    </group>
                    <group name="sales_and_op" string="Sales and Operations">
                        <group name="sales" string="Sales">
                            <field name="sale_order_default_team" string="Sales team" help="Define a dedicated Sales team assigned to all ANKORSTORE orders - useful for reporting"/>
                            <field name="res_users_id" string="Salesperson" help="Assign a dedicated salesperson to all Ankorstore orders, in case there is no salesperson already assigned to the customer account"/>
                        </group>
                        <group name="invoicing" string="Invoicing">
                            <field name="is_aks_invoicing_partner" string="Ankorstore as default invoicing partner?" widget="boolean_toggle" help="Define whether the invoicing partner should be the end customer (unticked), or Ankorstore (ticked)"/>
                            <field name="aks_partner" string="Invoicing partner" invisible="not is_aks_invoicing_partner" help="Find Ankorstore among the invoicing partners you have defined in Odoo; Or create it if it is not setup yet"/>
                            <field name="aks_fees_product" string="Ankorstore fees" help="Define the way you want to identify Ankorstore commissions in your Odoo orders. We recommend using a dedicated service product"/>
                            <field name="aks_default_invoice_journal" invisible="True"/>
                            <field name="aks_default_analytic_account" invisible="True"/>
                            <field name="aks_auto_confirm_quote" invisible="True"/>
                        </group>
                        <group name="stock" string="Marketplace Stock">
                            <field name="aks_default_warehouse_id" string="Default warehouse" help="Define the default warehouse to the AKS orders import"/>
                            <field name="aks_sync_allowed_location_ids" string="Stock location(s) to sync to Ankorstore" widget="many2many_tags" help="Select which stock location(s) you want to use to feed stock availability on Ankorstore. You may select one or many different locations. If you use Ankorstore Fulfilment Center, we recommend that you add it"/>
                        </group>
                        <group name="fulfillment_center" string="Ankorstore Fulfillment Center (FC)">
                            <field name="aks_active_fc" string="Is AnkorLogistics Fulfilment Center ?" widget="boolean_toggle" help="Confirm whether you are part of the AnkorLogistics Fulfilment Center programme"/>
                            <field name="aks_fc_wh_id" string="FC Odoo warehouse" help="Define/Select the Ankorstore FC in Odoo" invisible="not aks_active_fc"/>
                            <field name="aks_fc_location_id" string="FC internal location" help="Define/Select the Ankorstore FC internal location code in Odoo" invisible="not aks_active_fc"/>
                            <field name="aks_fc_shipping_method_id" string="FC shipping method" help="Define/Select the Ankorstore FC shipping method in Odoo" invisible="not aks_active_fc"/>
                            <field name="aks_auto_skip_reject" invisible="not aks_active_fc" string="Autoconfirm Ankorstore orders" help="/!\ Check “Autoconfirm Ankorstore orders” only if you are sure that your setting in Ankorstore is similar. If you are part of the AnkorsLogistics Fulfilment Programme, reflect here whether you want your Ankorstore orders to be automatically accepted when they are imported from Ankorstore"/>
                            <field name="aks_auto_confirm_orders" invisible="not aks_active_fc" string="Autoassign Ankorstore orders to FC" help="/!\ Check “Autoassign Ankorstore orders to FC” only if you are sure that your setting in Ankorstore is similar. If you are part of the AnkorsLogistics Fulfilment Programme, reflect here whether you want your Ankorstore orders to be automatically assigned to the Fulfilment Center, when the order is validated"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_backend_aks_tree" model="ir.ui.view">
        <field name="name">backend.aks.tree</field>
        <field name="model">backend.aks</field>
        <field name="arch" type="xml">
            <list>  <!-- tree became list in v18 -->
                <field name="name"/>
                <field name="company_id"/>
                <field name="base_url_prod"/>
            </list>
        </field>
    </record>

    <record id="view_backend_aks_whoiam" model="ir.ui.view">
        <field name="name">backend.aks.whoiam</field>
        <field name="model">backend.aks</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="response_data" readonly="1"/>
                </group>
            </form>
        </field>
    </record>

    <!-- ACTIONS -->
    <record id="action_backend_aks_form" model="ir.actions.act_window">
        <field name="name">Ankorstore Backend</field>
        <field name="res_model">backend.aks</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="connector_ankorstore.view_backend_aks_form"/>
    </record>
    <record id="action_backend_aks_tree" model="ir.actions.act_window">
        <field name="name">Ankorstore Backends</field>
        <field name="res_model">backend.aks</field>
        <field name="view_mode">list,form</field>   <!-- tree became list in v18 -->
        <field name="view_id" ref="connector_ankorstore.view_backend_aks_tree"/>
    </record>

    <!-- WEBHOOKS -->
    <!-- Form View -->
    <record id="view_backend_aks_webhook_form" model="ir.ui.view">
        <field name="name">backend.aks.webhook.form</field>
        <field name="model">backend.aks.webhook</field>
        <field name="arch" type="xml">
            <form string="Webhook">
                <header>
                    <button name="action_unsubscribe_webhooks" string="Unsubscribe" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <group>
                        <field name="event_type"/>
                        <field name="url"/>
                        <field name="subscription_active"/>
                        <field name="subscription_id"/>
                        <field name="backend_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_backend_aks_webhook_tree" model="ir.ui.view">
        <field name="name">backend.aks.webhook.tree</field>
        <field name="model">backend.aks.webhook</field>
        <field name="arch" type="xml">
            <list>  <!-- tree became list in v18 -->
                <field name="event_type"/>
                <field name="subscription_active"/>
            </list>
        </field>
    </record>

    <!-- Action View -->
    <record id="action_backend_aks_webhook_tree" model="ir.actions.act_window">
        <field name="name">Ankorstore Webhooks</field>
        <field name="res_model">backend.aks.webhook</field>
        <field name="view_mode">list,form</field>   <!-- tree became list in v18 -->
    </record>

    <!-- SYNC ERRORS -->
    <!-- Tree View -->
    <record id="view_aks_sync_error_tree" model="ir.ui.view">
        <field name="name">aks.sync.error.tree</field>
        <field name="model">aks.sync.error</field>
        <field name="arch" type="xml">
            <list>  <!-- tree became list in v18 -->
                <field name="model"/>
                <field name="operation"/>
                <field name="name"/>
                <field name="error_message"/>
                <field name="timestamp"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_aks_sync_error_form" model="ir.ui.view">
        <field name="name">aks.sync.error.form</field>
        <field name="model">aks.sync.error</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="model"/>
                        <field name="operation"/>
                        <field name="name"/>
                        <field name="error_message"/>
                        <field name="timestamp"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Action View -->
    <record id="action_aks_sync_error_tree" model="ir.actions.act_window">
        <field name="name">AKS Sync Errors</field>
        <field name="res_model">aks.sync.error</field>
        <field name="view_mode">list,form</field>   <!-- tree became list in v18 -->
    </record>

</odoo>
